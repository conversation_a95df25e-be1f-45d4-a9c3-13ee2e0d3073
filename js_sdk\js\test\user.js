// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export async function useUserModule(log) {
    const zim = ZIM.getInstance();

    const r = Math.ceil(Math.random() * 100);
    zim.updateUserName('tg001-' + r)
        .then(log.bind(null, 'updateUserName'))
        .catch(log.bind(null, 'updateUserName'));
    zim.updateUserAvatarUrl('tg001-avatar-' + r)
        .then(log.bind(null, 'updateUserAvatarUrl'))
        .catch(log.bind(null, 'updateUserAvatarUrl'));
    zim.updateUserExtendedData('tg001-extend-' + r)
        .then(log.bind(null, 'updateUserExtendedData'))
        .catch(log.bind(null, 'updateUserExtendedData'));
    zim.queryUsersInfo(['tg002'], { isQueryFromServer: true })
        .then(log.bind(null, 'queryUsersInfo'))
        .catch(log.bind(null, 'queryUsersInfo'));
}
