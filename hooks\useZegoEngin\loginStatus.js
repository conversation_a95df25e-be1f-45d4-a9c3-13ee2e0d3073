import { ref, reactive } from "vue";
import { useLiveStore } from "@/store/liveStore.js";
import { storeToRefs } from "pinia";
import { useOnMessage } from "./onMessage.js";
export function useLoginStatus() {
  const {
    // addListeners,
    sendBarrageMsg,
  } = useOnMessage();
  const liveStore = useLiveStore();
  const { zegoEngineInstance, zegoStreamID } = storeToRefs(liveStore);
  let isLogin = ref(false);
  // 登录房间
  async function loginRoom(roomID, userID, userName) {
    if (isLogin.value) {
      console.log("这里退出登录了", isLogin.value);
      await logoutRoom(roomID);
      isLogin.value = !isLogin.value;
    } else {
      let user = {
        userID: userID,
        userName: userName,
      };
      try {
        const result = await zegoEngineInstance.value.loginRoom(roomID, user, {
          token: "",
          isUserStatusNotify: true,
        });
        console.log("登录成功");
        console.log("登录成功", isLogin.value);
        // sendBarrageMsg(JSON.stringify(user), 'addUser')
        // #ifdef APP-PLUS
        isLogin.value = !isLogin.value;
        // #endif
        console.log("登录成功", isLogin.value);
      } catch (e) {
        //TODO handle the exception
        console.log(e);
      }
    }
  }
  // 退出房间
  function logoutRoom(roomID) {
    console.log("------------退出房间", roomID);
    console.log("isLogin", isLogin.value);
    // if (!isLogin.value) return
    zegoEngineInstance.value?.logoutRoom(roomID);
    // console.log('退出登录')
    // sendBarrageMsg(JSON.stringify({
    // 	userID: liveStore.roomInfo.userID,
    // 	userName: liveStore.roomInfo.userName
    // }), 'deleteUser')
  }

  return {
    loginRoom,
    logoutRoom,
  };
}
