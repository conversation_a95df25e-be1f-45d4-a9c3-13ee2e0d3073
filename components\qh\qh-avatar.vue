<template>
  <div class="content" :style="{ width: width, height: height, ...headStyle }">
    <image class="head-img rounded-full" :src="headUrl" />
    <!-- <image class="border-img" v-show="img" :src="img" /> -->

    <c-svga
      v-if="borderImg && borderImg.length > 0"
      class="border-img"
      ref="cSvgaRef"
      width="width"
      height="height"
      :src="borderImg"
      :loops="0"
      :auto-play="true"
      @frame="onFrame"
      @finished="onFinished"
      @percentage="onPercentage"
      @loaded="onSvgaLoaded"
    ></c-svga>
  </div>
</template>

<script setup>
defineProps({
  headUrl: {
    type: String,
    default: "",
  },
  borderImg: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "",
  },
  headStyle: {
    type: Object,
    default: {},
  },
});

const onFrame = (frame) => {
  //动画播放至某帧后回调
  // console.log(frame);
};
const onPercentage = (percentage) => {
  //动画播放至某进度后回调
  // console.log(percentage);
};
const onSvgaLoaded = () => {
  console.log("加载完成");
  // cSvgaRef.value.call('setContentMode', 'AspectFill')
};

const onFinished = () => {
  console.log("动画停止播放时回调");
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  overflow: hidden;
}
.head-img {
  margin: 12rpx;
  width: calc(100% - 24rpx);
  height: calc(100% - 24rpx);
}
.border-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
