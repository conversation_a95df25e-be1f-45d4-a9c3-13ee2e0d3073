<template>
  <swiper :current="active" v-bind="$attrs" previous-margin="230rpx" next-margin="230rpx" circular
    class="flex flex-row items-center h-360rpx" @change="onChange">
    <swiper-item v-for="(item, index) in list" :key="index" @click="onClick(index)">
      <image :src="item" class="w-288rpx h-360rpx rounded-[16rpx] transform transition-transform duration-300"
        :class="index === active ? 'scale-100' : 'scale-86'" />
    </swiper-item>
  </swiper>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      active: 0
    }
  },
  methods: {
    onChange(e) {
      this.active = e.detail.current;
      this.$emit('change', this.active)
    },
    onClick(index) {
      if (index === this.active) {
        this.$emit('item-click', index)
      } else {
        this.active = index
      }
    }
  }
}
</script>
