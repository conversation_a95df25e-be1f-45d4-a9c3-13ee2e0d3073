import ZegoExpressEngine from "@/components/zego-ZegoExpressUniApp-JS/lib/ZegoExpressEngine";
import {
  ZegoScenario,
  ZegoAudioConfig,
  ZegoAudioChannel,
  ZegoAudioCodecID,
} from "@/components/zego-ZegoExpressUniApp-JS/lib/ZegoExpressDefines";
import permision from "@/pages/live/permission.js";
import {
  liveroomUsers,
  liveroomCloseRoom,
  liveroomExitRoom,
} from "@/services/liveSetting";
import {
  getLiveroomIdInfo,
  getSysUserIdInfo,
  postRobotEnterRoom,
} from "@/services/room.js";
import { ref, reactive } from "vue";
import { useLiveStore } from "@/store/liveStore.js";
import { storeToRefs } from "pinia";
import { usePlaying } from "./play.js";
import { usePublish } from "./publish.js";
import { useLoginStatus } from "./loginStatus.js";
import { useOnMessage } from "./onMessage.js";
import { useZegoIm } from "@/hooks/useZegoIm";
// test;
// const appID = 1760599816;
// const appSign =
//   "5fccf073049180d62c7b98debc3533199192bee3d3adcd8ff4876c8f893a0b29";

// prod
const appID = 312060144;
const appSign =
  "b46584e62a9ba3ed3a4c9c6cbb7cffe796768db0dcd35a09aeb5e130d565dbff";

export function useZegoEngin() {
  const liveStore = useLiveStore();
  const { leaveRoom } = useZegoIm(liveStore.sendSuccessCallback);
  const { zegoStreamID, roomInfo, liveRoomDetail } = storeToRefs(liveStore);
  const { loginRoom, logoutRoom } = useLoginStatus();
  const { palyOption, onClickPlay, startPlayingStream, stopPlayingStream } =
    usePlaying();
  const {
    publishOption,
    onClickPublish,
    muteMicrophoneFn,
    isMicrophoneMutedFn,
    startPublishingStream,
    stopPublishingStream,
  } = usePublish();
  const { addListeners, sendBarrageMsg } = useOnMessage();
  // 自动混流任务对象
  // const ZegoAutoMixerTask = reactive({
  //   taskID: null, //自动混流任务的任务 ID
  //   roomID: null, //自动混流任务的房间 ID
  //   outputList: [], // 自动混流任务的输出流列表
  //   audioConfig: new ZegoMixerAudioConfig(), // 自动混流任务的音频配置
  //   enableSoundLevel: false, // 是否开启自动混流的声浪回调通知
  // });
  let engine = ref();

  let userStatus = reactive({
    type: "add",
    userList: [],
  });
  let liveUserList = ref([1]);
  let liveUserUpdateList = ref([]); // 直播间人数变化

  // 初始化房间
  async function initRoomEngine(roomID, userID, userName) {
    if (uni.getSystemInfoSync().platform === "android") {
      permision.requestAndroidPermission("android.permission.RECORD_AUDIO");
      permision.requestAndroidPermission("android.permission.CAMERA");
    }
    // 创建引擎
    await createEngine();
    liveStore.setEngineInfo(engine.value); // 保存引擎到store
    liveStore.changEnginStatus(true);

    // 登录房间
    await loginRoom(roomID, userID, userName);
    // 常用通知回调
    listenMethods();
  }
  async function createEngine() {
    let profile = {
      appID,
      // #ifdef APP-PLUS
      appSign,
      // #endif
      scenario: ZegoScenario.HighQualityChatroom,
    };
    engine.value = await ZegoExpressEngine.createEngineWithProfile(profile);
    engine.value.enableCamera(false);
    let zegoAudioConfig = new ZegoAudioConfig(
      128,
      ZegoAudioChannel.Mono,
      ZegoAudioCodecID.Default
    );
    engine.value.setAudioConfig(zegoAudioConfig);
    engine.value.setCaptureVolume(150);
  }

  // 常用通知回调
  async function listenMethods() {
    // 房间内当前在线用户数量回调。
    engine.value.on("roomOnlineUserCountUpdate", (roomID, count) => {
      // 注意事项：1. 此函数 30 秒回调一次。2. 因设计如此，当房间内用户超过 500 后，对房间内在线人数的统计会有一些误差。
      // console.log(roomID, "👩‍👩‍👦‍👦------打印", count);
    });
    //同一房间内的其他用户进出房间时，您可通过此回调收到通知
    engine.value.on("roomUserUpdate", (roomID, updateType, userList) => {
      let type = "add";
      let messageType = "addUser";
      console.log(roomID, "--------1直播间人数发生变化", userList);
      // 新增用户
      if (updateType === 0) {
        type = "add";
        messageType = "addUser";
      } else {
        // 删除用户
        type = "delete";
        messageType = "deleteUser";
      }
      sendBarrageMsg(
        JSON.stringify({
          userID: liveStore.roomInfo.userID,
          userName: liveStore.roomInfo.userName,
        }),
        messageType
      );
      userStatus.type = type;
      userStatus.userList = userList;

      getLiveroomUsers();
    });
    engine.value.on(
      "roomStateUpdate",
      (roomID, state, errorCode, extendedData) => {
        console.log(roomID, "zxdzxd房间状态发生变化", state);
      }
    );
    await engine.value.startSoundLevelMonitor({ millisecond: 100 });

    engine.value.on("remoteSoundLevelInfoUpdate", (soundinfo) => {
      // 远端拉流音频声浪回调
      // console.log("-----2222222声浪声浪声浪", Object.values(soundinfo)[0]);
      if (roomInfo.value && !roomInfo.value.isAnchor) {
        liveStore.setSoundinfo(Object.values(soundinfo)[0]);
        // liveStore.setSoundinfo(soundinfo[zegoStreamID.value]);
      }
    });

    engine.value.on("capturedSoundLevelInfoUpdate", (soundinfo) => {
      // 推流用户 在此方法中监听声音
      // console.log(
      //   roomInfo.value.isAnchor,
      //   "--------------------------推流用户监听声音",
      //   soundinfo
      // );
      // if (roomInfo.value && soundinfo.soundLevel > 0) {
      //   liveStore.setSoundinfo(soundinfo);
      // }
    });
    // 远端麦克风设备状态通知。
    engine.value.on("remoteMicStateUpdate", (streamID, state) => {
      // const roomId = liveRoomDetail.value?.id;
      // const anchorId = liveRoomDetail.value?.userId;
      console.log(streamID, "------------------远端麦克风状态", state);
      liveStore.setOthersStreamID(streamID);
      if (state === 0) {
        // 开启
        liveStore.setLiveMicrophoneList("add", streamID);
      } else {
        // 关闭
        liveStore.setLiveMicrophoneList("del", streamID);
      }
    });
    //用户推送音视频流的状态发生变更时，会收到该回调。
    engine.value.on(
      "publisherStateUpdate",
      (streamID, state, errorCode, extendedData) => {
        console.log(
          state,
          "------------------------监听推音视频流状态",
          errorCode
        );
        // state为2时推流已经成功，用户可以正常通信
        if (state === 2) {
          // 开启主播头像动态音效效果。
          if (roomInfo.value) {
            liveStore.setMicrophoneMuted(false);
          }
        }
        if (errorCode !== 0) {
          //推流状态出错
        }
      }
    );
    //拉流状态变更回调，会收到该回调
    engine.value.on(
      "playerStateUpdate",
      (streamID, state, errorCode, extendedData) => {
        // console.log(state, "-------------------------监听拉流状态", errorCode);
        if (errorCode !== 0) {
          //拉流状态出错
        }
      }
    );
    // 房间内其他用户推流/停止推流时，我们会在这里收到相应用户的音视频流增减的通知
    engine.value.on("roomStreamUpdate", (roomID, updateType, streamList) => {
      // [{"extraInfo":"","user":{"userID":"20","userName":"小马里奥888"},"streamID":"uni_44_20"}]
      console.log(roomID, "--------------房间内其他用户推流11", updateType);
      console.log(
        liveRoomDetail.value?.anchorUserVo.id,
        "--------------房间内其他用户推流22",
        streamList
      );
      // updateType：0 为add, 1为delete
      if (updateType === 0) {
        const listA = [];
        streamList.forEach((item) => {
          startPlayingStream(item.streamID);
          if (liveRoomDetail.value?.anchorUserVo.id == item.user.userID) {
            //
          } else {
            // 包含cloud_player说明是创建的云播放器推的流
            if (item.user.userID.includes("cloud_player")) {
              const newList = item.streamID.split("_");
              // listA.push(newList[newList.length - 1]);
            } else {
              listA.push(item.user.userID);
            }
          }
        });
        console.log("--------------房间内其他用户推流222", listA);
        // 保存连麦进行中的用户id
        liveStore.setLinkCallUserIdList(listA);
      }
      if (updateType === 1) {
        // 直播流断了之后(主播关播) 用户退出直播间v
        console.log(
          "---------------------11直播流断了之后(主播关播)",
          streamList[0].user.userID
        );
        console.log(
          "---------------------22直播流断了之后(主播关播)",
          liveRoomDetail.value?.anchorUserVo.id
        );
        if (streamList[0].user.userID.includes("cloud_player")) {
          // 数字人直播
          const newList = streamList[0].streamID.split("_");
          if (
            newList[newList.length - 1] == liveRoomDetail.value?.anchorUserVo.id
          ) {
            setTimeout(() => {
              getLiveroomIdInfo({ id: newList[1] }).then((str) => {
                // 1: 直播中；3: 关播
                if (str.status == 3) {
                  closeLiveHome().then(() => {
                    liveStore.setIsLiveClose(true);
                    liveStore.resetRoom();
                    uni.redirectTo({
                      url: "/pages/liveCount/index",
                    });
                  });
                }
              });
            }, 3000);
          }
        } else {
          // 真人直播
          if (
            streamList[0].user.userID == liveRoomDetail.value?.anchorUserVo.id
          ) {
            closeLiveHome().then(() => {
              liveStore.setIsLiveClose(true);
              liveStore.resetRoom();
              uni.redirectTo({
                url: "/pages/liveCount/index",
              });
            });
            return;
          }
          let copyArr = [...liveStore.liveLinkCallUserIdList];
          streamList.forEach((item) => {
            stopPlayingStream(item.streamID);
            const index = copyArr.indexOf(item.user.userID);
            if (index > -1) {
              copyArr.splice(index, 1); // 从 index 处开始删除一个元素
            }
          });
          // 保存连麦进行中的用户id
          liveStore.setLinkCallUserIdList(copyArr);
          liveStore.setIsLiveCallStatus(false);
        }
      }
    });
  }

  async function getLiveroomUsers(roomId) {
    try {
      let data = await liveroomUsers(roomId || roomInfo.value.roomID);
      console.log("👩‍🦲------在线人数Engin打印", data);
      liveUserList.value = data;
      liveStore.setUserList(data);
    } catch (e) {
      console.log("错误", e);
    }
  }
  // 销毁引擎
  function destroyEngine() {
    try {
      logoutRoom(zegoStreamID.value);
      console.log("------------------00销毁引擎", roomInfo.value);
      // 退出im聊天
      leaveRoom(roomInfo.value?.roomID, roomInfo.value);

      ZegoExpressEngine.destroyEngine();
      engine.value = null;
    } catch (e) {
      console.log("销毁destroyEngine错误", e);
    }
  }
  // 主播停止直播后 销毁所有人所在的直播间
  async function closeLiveHome(islive) {
    try {
      let data = await closeRoom(); // 退出直播
      liveStore.setLiveCountData(data);
      console.log("---------------主播停止直播后 销毁所有人所在的直播间", data);
      destroyEngine();
      // 在直播间点击关闭 销毁之后后 回到直播列表页面
      return data;
    } catch (e) {
      console.log("关闭直播间错误", e);
    }
  }

  // 关闭直播间
  async function closeRoom() {
    let data = await liveroomCloseRoom(roomInfo.value.roomID);
    return data;
  }
  // 退出直播间
  async function exitRoom() {
    let data = await liveroomExitRoom(roomInfo.value.roomID);
    return data;
  }

  return {
    initRoomEngine,
    createEngine,
    destroyEngine,
    ZegoExpressEngine,
    palyOption,
    onClickPlay,
    startPlayingStream,
    stopPlayingStream,
    publishOption,
    onClickPublish,
    muteMicrophoneFn,
    isMicrophoneMutedFn,
    startPublishingStream,
    stopPublishingStream,
    addListeners,
    sendBarrageMsg,
    getLiveroomUsers,
    liveUserList,
  };
}
