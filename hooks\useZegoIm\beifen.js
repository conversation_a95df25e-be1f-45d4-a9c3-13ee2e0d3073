//  创建房间
	function createRoom(roomInfo, roomAttr = {}) {
		const {
			roomID,
			roomName
		} = roomInfo;
		return zim
			.createRoom({
				roomID,
				roomName,
			})
			.catch((e) => {
				console.log("创建房间失败", e);
			});
	}
	// 加入房间
	async function joinRoom(roomID) {
		try {
			let data = await zim.joinRoom(roomID);
			console.log("加入房间成功000000000");
			customSendChatMessage({
				type: 1,
				message: `进入直播间`,
				sendType: 3,
			});
			return data;
		} catch (e) {
			console.log("加入房间错误", e);
		}
	}