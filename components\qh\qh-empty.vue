<template>
  <div class="flex flex-col items-center pt-232rpx">
    <image :src="img" class="w-300rpx h-300rpx" />
    <div class="mt-32rpx text-28rpx leading-40rpx text-hex-999">{{ text }}</div>
    <div v-if="button"
      class="mt-64rpx w-288rpx leading-80rpx rounded-[40rpx] text-center text-32rpx text-primary border-2rpx border-primary"
      @click="emit('button')">{{
    button
      }}</div>
  </div>
</template>

<script setup>
import defaultIconEmpty from "@/static/img/icon-empty-default.png"
defineProps({
  img: {
    type: String,
    default: defaultIconEmpty
  },
  text: {
    type: String,
    default: '什么都没有哦~'
  },
  button: {
    type: String,
    default: ''
  }
})
const emit = defineEmits('button')
</script>

<style lang="scss" scoped>

</style>