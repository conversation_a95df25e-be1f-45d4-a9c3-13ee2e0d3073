<template>
  <div class="flex flex-row flex-wrap justify-between" v-bind="$attrs">
    <div
      v-for="(item, index) in list"
      :key="item.id"
      @click="$emit('item-click', index)"
      class="w-332rpx pb-32rpx relative"
    >
      <image
        :src="item.coverDetail"
        class="w-full h-248rpx rounded-[16rpx] bg-hex-D8D8D8"
        mode="aspectFill"
      />
      <image
        class="absolute w-72rpx h-72rpx top-0 left-0"
        :src="item.isFree ? mfIcon : xmIcon"
      ></image>
      <qh-subtitle v-bind="{ ...item, tags: [] }" />
    </div>
  </div>
</template>

<script>
import xmIcon from "@/static/img/home/<USER>";
import mfIcon from "@/static/img/home/<USER>";
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      xmIcon,
      mfIcon,
    };
  },
};
</script>
