import { ZegoAccurateSeekConfig, ZegoAECMode, ZegoANSMode, ZegoAudioConfig, ZegoAudioRoute, ZegoEffectsBeautyParam, ZegoCDNConfig, ZegoEngineConfig, ZegoEngineProfile, ZegoIMSendBarrageMessageResult, ZegoIMSendBroadcastMessageResult, ZegoIMSendCustomCommandResult, ZegoMediaPlayer, ZegoMediaPlayerAudioChannel, ZegoMediaPlayerLoadResourceResult, ZegoMediaPlayerSeekToResult, ZegoMediaPlayerState, ZegoMediaPlayerTakeSnapshotResult, ZegoNetWorkResourceCache, ZegoOrientation, ZegoPlayerConfig, ZegoPlayerTakeSnapshotResult, ZegoPublishChannel, ZegoPublisherTakeSnapshotResult, ZegoPublisherUpdateCdnUrlResult, ZegoRoomConfig, ZegoRoomSetRoomExtraInfoResult, ZegoScenario, ZegoTrafficControlMinVideoBitrateMode, ZegoTrafficControlProperty, ZegoUser, ZegoVideoConfig, ZegoVideoConfigPreset, ZegoVideoMirrorMode, ZegoVideoStreamType, ZegoVoiceChangerParam, ZegoWatermark, ZegoAutoMixerTask, ZegoMixerStartResult, ZegoMixerStopResult, ZegoMixerTask, ZegoRoomMode, ZegoPublisherConfig, ZegoSoundLevelConfig, ZegoRoomLoginResult, ZegoRoomLogoutResult, ZegoMediaPlayerResource, ZegoLogConfig, ZegoDataRecordConfig } from "../ZegoExpressDefines";
import { ZegoEventListener, ZegoMediaPlayerListener } from "../ZegoExpressEventHandler";
declare type ZegoAnyCallback = (...args: any[]) => any;
export declare class ZegoExpressEngineImpl {
    static _listeners: Map<string, Map<ZegoAnyCallback, ZegoAnyCallback>>;
    static _mediaPlayerMap: Map<number, ZegoMediaPlayer>;
    private static _callMethod;
    static getInstance(): ZegoExpressEngineImpl;
    static createEngineWithProfile(profile: ZegoEngineProfile): Promise<ZegoExpressEngineImpl>;
    static createEngine(appID: number, appSign: string, isTestEnv: boolean, scenario: ZegoScenario): Promise<ZegoExpressEngineImpl>;
    static destroyEngine(): Promise<void>;
    static setLogConfig(config: ZegoLogConfig): Promise<void>;
    static getVersion(): Promise<string>;
    static setEngineConfig(config: ZegoEngineConfig): Promise<void>;
    static setRoomMode(mode: ZegoRoomMode): Promise<void>;
    on<EventType extends keyof ZegoEventListener>(event: EventType, callback: ZegoEventListener[EventType]): void;
    off<EventType extends keyof ZegoEventListener>(event: EventType, callback?: ZegoEventListener[EventType]): void;
    uploadLog(): Promise<void>;
    callExperimentalAPI(params: string): Promise<string>;
    setDummyCaptureImagePath(filePath: string, channel?: ZegoPublishChannel): Promise<void>;
    loginRoom(roomID: string, user: ZegoUser, config?: ZegoRoomConfig): Promise<ZegoRoomLoginResult>;
    logoutRoom(roomID?: string): Promise<ZegoRoomLogoutResult>;
    loginMultiRoom(roomID: string, config: ZegoRoomConfig): Promise<void>;
    switchRoom(fromRoomID: string, toRoomID: string, config?: ZegoRoomConfig): Promise<void>;
    renewToken(roomID: string, token: string): Promise<void>;
    setRoomExtraInfo(roomID: string, key: string, value: string): Promise<ZegoRoomSetRoomExtraInfoResult>;
    startPublishingStream(streamID: string, channel?: ZegoPublishChannel, config?: ZegoPublisherConfig): Promise<void>;
    stopPublishingStream(channel?: ZegoPublishChannel): Promise<void>;
    startPreview(channel?: ZegoPublishChannel): Promise<void>;
    stopPreview(channel?: ZegoPublishChannel): Promise<void>;
    setVideoConfig(config: ZegoVideoConfigPreset | ZegoVideoConfig, channel?: ZegoPublishChannel): Promise<void>;
    getVideoConfig(channel?: ZegoPublishChannel): Promise<ZegoVideoConfig>;
    setVideoMirrorMode(mode: ZegoVideoMirrorMode, channel?: ZegoPublishChannel): Promise<void>;
    setAppOrientation(orientation: ZegoOrientation, channel?: ZegoPublishChannel): Promise<void>;
    setAudioConfig(config: ZegoAudioConfig, channel?: ZegoPublishChannel): Promise<void>;
    getAudioConfig(channel?: ZegoPublishChannel): Promise<ZegoAudioConfig>;
    setPublishStreamEncryptionKey(key: string, channel?: ZegoPublishChannel): Promise<void>;
    takePublishStreamSnapshot(channel?: ZegoPublishChannel): Promise<ZegoPublisherTakeSnapshotResult>;
    mutePublishStreamAudio(mute: boolean, channel?: ZegoPublishChannel): Promise<void>;
    mutePublishStreamVideo(mute: boolean, channel?: ZegoPublishChannel): Promise<void>;
    enableTrafficControl(enable: boolean, property: ZegoTrafficControlProperty, channel?: ZegoPublishChannel): Promise<void>;
    setMinVideoBitrateForTrafficControl(bitrate: number, mode: ZegoTrafficControlMinVideoBitrateMode, channel?: ZegoPublishChannel): Promise<void>;
    setCaptureVolume(volume: number): Promise<void>;
    addPublishCdnUrl(targetURL: string, streamID: string): Promise<ZegoPublisherUpdateCdnUrlResult>;
    removePublishCdnUrl(targetURL: string, streamID: string): Promise<ZegoPublisherUpdateCdnUrlResult>;
    enablePublishDirectToCDN(enable: boolean, config: ZegoCDNConfig, channel?: ZegoPublishChannel): Promise<void>;
    setPublishWatermark(watermark: ZegoWatermark, isPreviewVisible: boolean, channel?: ZegoPublishChannel): Promise<void>;
    sendSEI(data: ArrayBuffer, channel?: ZegoPublishChannel): Promise<void>;
    enableHardwareEncoder(enable: boolean): Promise<void>;
    startPlayingStream(streamID: string, config?: ZegoPlayerConfig): Promise<void>;
    stopPlayingStream(streamID: string): Promise<void>;
    setPlayStreamDecryptionKey(key: string, streamID: string): Promise<void>;
    takePlayStreamSnapshot(streamID: string): Promise<ZegoPlayerTakeSnapshotResult>;
    setPlayVolume(streamID: string, volume: number): Promise<void>;
    setAllPlayStreamVolume(volume: number): Promise<void>;
    setPlayStreamVideoType(streamID: string, streamType: ZegoVideoStreamType): Promise<void>;
    setPlayStreamBufferIntervalRange(streamID: string, minBufferInterval: number, maxBufferInterval: number): Promise<void>;
    setPlayStreamFocusOn(streamID: string): Promise<void>;
    mutePlayStreamAudio(streamID: string, mute: boolean): Promise<void>;
    mutePlayStreamVideo(streamID: string, mute: boolean): Promise<void>;
    muteAllPlayStreamAudio(mute: boolean): Promise<void>;
    muteAllPlayStreamVideo(mute: boolean): Promise<void>;
    enableHardwareDecoder(enable: boolean): Promise<void>;
    enableCheckPoc(enable: boolean): Promise<void>;
    startMixerTask(task: ZegoMixerTask): Promise<ZegoMixerStartResult>;
    stopMixerTask(task: ZegoMixerTask): Promise<ZegoMixerStopResult>;
    startAutoMixerTask(task: ZegoAutoMixerTask): Promise<ZegoMixerStartResult>;
    stopAutoMixerTask(task: ZegoAutoMixerTask): Promise<ZegoMixerStopResult>;
    muteMicrophone(mute: boolean): Promise<void>;
    isMicrophoneMuted(): Promise<boolean>;
    muteSpeaker(mute: boolean): Promise<void>;
    isSpeakerMuted(): Promise<boolean>;
    enableAudioCaptureDevice(enable: boolean): Promise<void>;
    getAudioRouteType(): Promise<ZegoAudioRoute>;
    setAudioRouteToSpeaker(defaultToSpeaker: boolean): Promise<void>;
    enableCamera(enable: boolean, channel?: ZegoPublishChannel): Promise<void>;
    useFrontCamera(enable: boolean, channel?: ZegoPublishChannel): Promise<void>;
    setCameraZoomFactor(factor: number, channel?: ZegoPublishChannel): Promise<void>;
    getCameraMaxZoomFactor(channel?: ZegoPublishChannel): Promise<number>;
    startSoundLevelMonitor(config?: ZegoSoundLevelConfig): Promise<void>;
    stopSoundLevelMonitor(): Promise<void>;
    startAudioSpectrumMonitor(millisecond: number): Promise<void>;
    stopAudioSpectrumMonitor(): Promise<void>;
    enableHeadphoneMonitor(enable: boolean): Promise<void>;
    setHeadphoneMonitorVolume(volume: number): Promise<void>;
    enableAEC(enable: boolean): Promise<void>;
    enableHeadphoneAEC(enable: boolean): Promise<void>;
    setAECMode(mode: ZegoAECMode): Promise<void>;
    enableAGC(enable: boolean): Promise<void>;
    enableANS(enable: boolean): Promise<void>;
    enableTransientANS(enable: boolean): Promise<void>;
    setANSMode(mode: ZegoANSMode): Promise<void>;
    startEffectsEnv(): Promise<void>;
    stopEffectsEnv(): Promise<void>;
    enableEffectsBeauty(enable: boolean): Promise<void>;
    setEffectsBeautyParam(param: ZegoEffectsBeautyParam): Promise<void>;
    setAudioEqualizerGain(bandGain: number, bandIndex: number): Promise<void>;
    setVoiceChangerParam(param: ZegoVoiceChangerParam, audioChannel: ZegoMediaPlayerAudioChannel): Promise<void>;
    enableVirtualStereo(enable: boolean, angle: number): Promise<void>;
    sendBroadcastMessage(roomID: string, message: string): Promise<ZegoIMSendBroadcastMessageResult>;
    sendBarrageMessage(roomID: string, message: string): Promise<ZegoIMSendBarrageMessageResult>;
    sendCustomCommand(roomID: string, command: string, toUserList?: ZegoUser[]): Promise<ZegoIMSendCustomCommandResult>;
    startScreenCapture(): Promise<void>;
    stopScreenCapture(): Promise<void>;
    enableTorch(enable: boolean, channel?: ZegoPublishChannel): Promise<void>;
    createMediaPlayer(): Promise<ZegoMediaPlayer | undefined>;
    destroyMediaPlayer(mediaPlayer: ZegoMediaPlayer): Promise<void>;
    startRecordingCapturedData(config: ZegoDataRecordConfig, channel?: ZegoPublishChannel): Promise<void>;
    stopRecordingCapturedData(channel?: ZegoPublishChannel): Promise<void>;
}
export declare class ZegoMediaPlayerImpl implements ZegoMediaPlayer {
    private _index;
    constructor(index: number);
    private _callMethod;
    on<MediaPlayerEventType extends keyof ZegoMediaPlayerListener>(event: MediaPlayerEventType, callback: ZegoMediaPlayerListener[MediaPlayerEventType]): void;
    off<MediaPlayerEventType extends keyof ZegoMediaPlayerListener>(event: MediaPlayerEventType, callback?: ZegoMediaPlayerListener[MediaPlayerEventType]): void;
    loadResource(path: string): Promise<ZegoMediaPlayerLoadResourceResult>;
    loadResourceWithConfig(config: ZegoMediaPlayerResource): Promise<ZegoMediaPlayerLoadResourceResult>;
    start(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    setPlayerView(playerID: number): Promise<void>;
    seekTo(millisecond: number): Promise<ZegoMediaPlayerSeekToResult>;
    enableRepeat(enable: boolean): Promise<void>;
    enableAux(enable: boolean): Promise<void>;
    setPlaySpeed(speed: number): Promise<void>;
    muteLocal(mute: boolean): Promise<void>;
    setVolume(volume: number): Promise<void>;
    setPlayVolume(volume: number): Promise<void>;
    setPublishVolume(volume: number): Promise<void>;
    setProgressInterval(millisecond: number): Promise<void>;
    setAudioTrackIndex(index: number): Promise<void>;
    setVoiceChangerParam(param: ZegoVoiceChangerParam, audioChannel: ZegoMediaPlayerAudioChannel): Promise<void>;
    takeSnapshot(): Promise<ZegoMediaPlayerTakeSnapshotResult>;
    setNetworkResourceMaxCache(time: number, size: number): Promise<void>;
    setNetworkBufferThreshold(threshold: number): Promise<void>;
    enableAccurateSeek(enable: boolean, config: ZegoAccurateSeekConfig): Promise<void>;
    getNetworkResourceCache(): Promise<ZegoNetWorkResourceCache>;
    getPlayVolume(): Promise<number>;
    getPublishVolume(): Promise<number>;
    getTotalDuration(): Promise<number>;
    getCurrentProgress(): Promise<number>;
    getAudioTrackCount(): Promise<number>;
    getCurrentState(): Promise<ZegoMediaPlayerState>;
    getIndex(): number;
}
export {};
