import { useAppStore } from "@/store/app";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/user";
import {ref} from 'vue'
import { onShow, onLoad } from "@dcloudio/uni-app";
import { useLiveStore } from "@/store/liveStore";
import { getRoomList } from "@/services/liveSetting";

export function useRecommend() {
	const { phone } = storeToRefs(useUserStore());
	const appStore = useAppStore();
	const liveStore = useLiveStore();
	let { roomInfo, zegoEngineInstance, haveEngine } = storeToRefs(liveStore);

	async function goLiveRoom(recommendItem) {
	  let item = recommendItem;
	  if (haveEngine.value) {
	    if (item.id * 1 === roomInfo.value?.roomID * 1) {
	      // 自己是主播 进入自己的直播间
	      uni.navigateTo({
	        url: `/pages/live/live-page/index?roomid=${item.id}`,
	      });
	    } else {
	      await quickCloseLive();
	      uni.navigateTo({
	        url: `/pages/live/live-page/index?roomid=${item.id}`,
	      });
	    }
	    return;
	  }
	
	  uni.navigateTo({
	    url: `/pages/live/live-page/index?roomid=${item.id}`,
	  });
	}
	
	
	async function getRecommend() {
		let recommendItem = null
		try{
			if (appStore.getReleaseMode() == "1" && phone.value) {
			  let data = await getRoomList(roomListParam(1));
			  console.log("room list", data);
			  if (!data || !data.list || !data.list.length) {
			    let list = await getRoomList(roomListParam(2));
			    console.log("room list", list);
			    if (list && list.list && list.list.length) {
			      let val = list.list[0];
			      val.follow = false;
			      recommendItem = val;
			    
			    }
			  } else {
			    let val = data.list[0];
			    val.follow = true;
			    recommendItem = val;
			   
			  }
			}
			return recommendItem
			
		}catch(e){
			console.log(e.message)
			throw new Error('获取推荐列表错误')
		}
		
	}
	function roomListParam(type) {
	  let params = {
	    filter: {
	      searchContent: "",
	      type,
	    },
	    page: {
	      pageIndex: 1,
	      pageSize: 10,
	    },
	  };
	  return params;
	}
	
	
	return {
		getRecommend,
		goLiveRoom
	}
	
}