<template>
  <template v-if="!tag">
    <div class="flex flex-wrap pb-32rpx">
      <div
        class="px-32rpx mt-32rpx relative"
        v-for="(item, index) in options"
        :key="index"
        @click="onChange(index)"
      >
        <div
          class="text-28rpx leading-40rpx transition-colors"
          :class="modelValue === index ? 'text-primary' : 'text-hex-333'"
        >
          {{ item }}
        </div>
        <div
          class="absolute left-40rpx right-40rpx -bottom-8rpx h-4rpx rounded-[4rpx] transform transition-transform"
          :class="modelValue === index ? 'scale-x-100' : 'scale-x-0'"
          style="
            background: linear-gradient(90deg, #25f776 12.82%, #25f7ec 100%);
          "
        ></div>
      </div>
    </div>
  </template>
  <template v-else>
    <div class="pb-32rpx pr-32rpx flex overflow-x-auto">
      <div
        class="first:ml-32rpx mr-34rpx last:mr-0 mt-32rpx flex-shrink-0"
        v-for="(item, index) in options"
        :key="index"
        @click="onChange(index)"
      >
        <div
          class="h-48rpx box-border flex items-center text-24rpx transition-colors rounded-[24rpx]"
          :class="
            modelValue === index
              ? 'text-primary bg-primary bg-opacity-10 border-2rpx border-primary border-opacity-65 px-18rpx'
              : 'text-hex-999 bg-black bg-opacity-3 px-20rpx'
          "
        >
          {{ item }}
        </div>
      </div>
    </div>
  </template>
</template>

<script setup>
const props = defineProps({
  options: {
    type: Array,
    default: [],
  },
  modelValue: Number,
  tag: Boolean,
});

const emit = defineEmits(['update:modelValue', 'change']);
function onChange(index) {
  // tag 标签模式下，支持取消选中
  if (props.tag && props.modelValue === index) {
    index = -1;
  }
  emit('update:modelValue', index);
  emit('change', index);
}
</script>

<style lang="scss" scoped></style>
