<template>
  <div class="flex flex-row flex-wrap justify-between" v-bind="$attrs">
    <div
      v-for="(item, index) in list"
      class="relative"
      :key="item.id"
      @click="$emit('item-click', index)"
    >
      <image
        :src="item.coverList"
        class="w-214rpx h-272rpx rounded-[16rpx] bg-hex-D8D8D8"
        mode="aspectFill"
      />
      <image
        class="absolute w-72rpx h-72rpx top-0 left-0"
        :src="item.isFree ? mfIcon : xmIcon"
      ></image>
      <div
        class="mt-16rpx mb-32rpx text-28rpx leading-40rpx overflow-ellipsis truncate w-214rpx"
      >
        {{ item.title }}
      </div>
    </div>
  </div>
</template>

<script>
import xmIcon from "@/static/img/home/<USER>";
import mfIcon from "@/static/img/home/<USER>";
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      xmIcon,
      mfIcon,
    };
  },
};
</script>
