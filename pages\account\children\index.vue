<template>
  <div>
    <qh-navbar title="青少年模式" center offset :border="(step !== 0)" background="white" dark />
    <Intro v-if="(step === 0)" :opened="isOpened" @next="onNext()" @modify="onVerify(4)" />
    <Inputer v-if="(step > 0)" ref="inputer" :title="title" :desc="desc" :danger="danger"
      :show-forget="title === '验证身份'" @finish="onInputFinish" @forget="onForget" />
  </div>
</template>

<script setup>
import { computed, nextTick, ref } from 'vue';
import { postExitChildMode, postSetChildModePassword, postVerifyChildMode } from '../../../services/account';
import { useUserStore } from '../../../store/user';
import { TweetMark } from '../../../utils/types';
import { showModal, showToast } from '../../../utils/utils';
import Inputer from './_components/Inputer.vue';
import Intro from './_components/Intro.vue';

const step = ref(0)
const title = ref('')
const desc = ref('')
const danger = ref(false)
const inputer = ref()

const userStore = useUserStore();
const isOpened = computed(() => userStore.childMode)

const onSet = (isReset) => {
  step.value = 1;
  title.value = "设置密码"
  desc.value = isReset ? '请输入新密码' : '开启青少年模式需要设置密码'
  danger.value = false
  resetInputer();
}

const resetInputer = async () => {
  setTimeout(() => {
    inputer.value.reset();
  }, 100)
}

const onVerify = (_step) => {
  step.value = _step;
  title.value = '验证身份'
  desc.value = '输入密码验证身份'
  resetInputer();
}

const onVerifyError = () => {
  desc.value = '密码错误，请重试'
  danger.value = true;
  resetInputer();
}

const onNext = () => {
  if (isOpened.value) {
    showModal({
      title: "提示",
      content: "确定退出青少年模式？",
      onConfirm: () => {
        onVerify(3)
      }
    })
  } else {
    onSet();
  }
}

const onToggle = async (open) => {
  if (!open) {
    await postExitChildMode(password.value)
    showToast("青少年模式已退出")
  } else {
    await postSetChildModePassword(password.value, oldPassword.value)
    showToast(isOpened.value ? '密码修改成功' : '青少年模式已开启')
  }
  userStore.setUserInfo({
    childMode: open
  })
  setTimeout(() => {
    uni.navigateBack();
  }, 1000)
}

const password = ref('')
const oldPassword = ref('')
const onInputFinish = async (value) => {
  if (step.value === 1) {
    password.value = value
    step.value = 2;
    title.value = '确认密码'
    desc.value = '请再次输入刚才的密码'
    resetInputer();
  } else if (step.value === 2) {
    if (value !== password.value) {
      desc.value = "两次输入的密码不一致，请重新输入"
      danger.value = true;
      resetInputer();
    } else {
      onToggle(true);
    }
  } else if (step.value === 3) {
    // 身份验证
    try {
      password.value = value;
      await onToggle(false);
    } catch (error) {
      onVerifyError();
    }
  } else if (step.value === 4) {
    // 修改密码验证
    try {
      await postVerifyChildMode(value);
      oldPassword.value = value
      onSet(true);
    } catch (error) {
      onVerifyError();
    }
  }
}

const onForget = () => {
  uni.routeTo('tweet', { id: TweetMark.child_mode_password });
}

</script>

<style lang="scss" scoped>

</style>