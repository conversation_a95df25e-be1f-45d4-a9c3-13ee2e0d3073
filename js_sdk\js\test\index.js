// #ifdef APP-PLUS
import ZIM from '../../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../zego-zim-miniprogram';
// #endif
import { useUserModule } from './user';
import { useConversationModule } from './conversation';
import { useGroupModule } from './group';
import { useRoomModule } from './room';
import { useCallModule } from './call';
import { sendImageMessage, sendVideoMessage } from './file';
import { appConfig, avatarPrefix } from '../config';

/**
 * 1、appid: 291905050, 登录账号: tg001, 涉及的账号: tg002, tg003, tg004
 * 2、其他端提前创建房间: rtg001, 其他端提前创建群组: gtg001
 * 3、其他端发起呼叫邀请, 本端进行 callAccept / callReject
 * 4、其他端发单聊文件消息, 本端进行 downloadMediaFile
 */

export async function _test_() {
    let seq = 0;
    const time = new Date().toISOString().substring(0, 19);
    const noop = () => {};
    const log = function () {
        const { 0: ev, ...res } = arguments;
        console.log('JSAPI.log.' + ev, stringify(res));
    };
	
	const zim = ZIM.create(appConfig);

    // const zim = ZIM.getInstance();

    // Init
    ZIM.getVersion().then((res) => log('version', res));
    const logeEvents = [
        'error',
        'connectionStateChanged',
        'conversationChanged',
        'conversationTotalUnreadMessageCountUpdated',
        'receiveGroupMessage',
        'receiveRoomMessage',
        'roomStateChanged',
        'roomMemberJoined',
        'roomMemberLeft',
        'roomAttributesUpdated',
        'roomAttributesBatchUpdated',
        'roomMemberAttributesUpdated',
        'groupStateChanged',
        'groupNameUpdated',
        'groupAvatarUrlUpdated',
        'groupNoticeUpdated',
        'groupAttributesUpdated',
        'groupMemberStateChanged',
        'groupMemberInfoUpdated',
        'callInvitationCancelled',
        'callInvitationTimeout',
        'callInvitationAccepted',
        'callInvitationRejected',
        'callInviteesAnsweredTimeout',
    ];
    logeEvents.forEach((ev) => zim.on(ev, noop));
    zim.on('receivePeerMessage', (ctx, res) => {
        res.messageList.some((msg) => {
            if (msg.fileUID) {
                zim.downloadMediaFile(msg, 1, log.bind(null, 'downloadProgress')).then(
                    log.bind(null, 'downloadMediaFile'),
                );
                return true;
            }
        });
    });
    zim.on('callInvitationReceived', (ctx, res) => {
        seq++;
        if (seq % 2) {
            zim.callAccept(res.callID).then(log.bind(null, 'callAccept'));
        } else {
            zim.callReject(res.callID).then(log.bind(null, 'callReject'));
        }
    });

    await zim.login({ userID: 'tg001', userName: 'tg001' }, '');

    await useUserModule(log, time);
    await useConversationModule(log, time);
    await useGroupModule(log, time);
    await useRoomModule(log, time);
    await useCallModule(log, time);

    // Destroy
    setTimeout(async () => {
        logeEvents.forEach((ev) => zim.off(ev));
        zim.uploadLog();
        zim.logout();
        zim.destroy();
        log('destroy');
    }, 10000);

    // sendImageMessage(zim, log);
    // sendVideoMessage(zim, log);
}

function stringify(obj) {
    const str = JSON.stringify(obj, (key, value) => {
        if (value instanceof Uint8Array) return 'b=' + value.length;
        if (typeof value == 'string' && value.startsWith('http') && value.length > 128) return 's=' + value.length;
        return value;
    });
    return str;
}
