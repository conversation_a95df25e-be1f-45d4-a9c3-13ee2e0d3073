<!-- 模态框确认 -->
<template>
  <up-popup
    :show="showConfirmPop"
    mode="center"
    :closeOnClickOverlay="false"
    :round="8"
  >
    <view class="bg-hex-ffffff w-80vw h-340rpx rounded-[16rpx]">
      <div class="h-210rpx popup-content-class">
        <div class="flex justify-center ml-32rpx mr-32rpx">
          <div class="mt-108rpx ml-0 mr-0 text-32rpx">{{ titlePop }}</div>
        </div>
      </div>
      <div class="flex justify-center h-84rpx">
        <div
          class="text-28rpx flex justify-center items-center mr-24rpx round_btn close_btn"
          @click="cancelPopup()"
        >
          <div>取消</div>
        </div>
        <div
          class="text-28rpx flex justify-center items-center ml-24rpx round_btn confirm_btn"
          @click="confirmFn()"
        >
          <div>{{ confirmText }}</div>
        </div>
      </div>
    </view>
  </up-popup>
</template>

<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
const showConfirmPop = ref(true);
const emits = defineEmits(["closeConfirmPop", "confirmModalPop"]);

let props = defineProps({
  titlePop: {
    type: String,
    default: () => "暂无title",
  },
  userItem: {
    // 查看Id
    type: Object,
    default: () => null,
  },
  anchorUserInfo: {
    // 主播信息
    type: Object,
    default: () => null,
  },
  confirmText: {
    type: String,
    default: () => "确定",
  },
});

onLoad((option) => {
  //
});

// 确定
const confirmFn = () => {
  showConfirmPop.value = false;
  emits("confirmModalPop");
};

// 取消关闭
const cancelPopup = () => {
  showConfirmPop.value = false;
  emits("closeConfirmPop");
};
</script>
<style lang="scss" scoped>
.round_btn {
  width: 200rpx;
  border-radius: 44rpx;
}
.close_btn {
  background-color: #d7d7d7;
}
.confirm_btn {
  color: #fff;
  background-color: #29c8a2;
}
</style>
