<template>
  <div v-bind="$attrs">
    <slot :list="list" />
    <qh-empty
      v-if="empty"
      v-show="!loading && !list.length"
      v-bind="empty"
      @button="empty?.onButton"
    />
    <div class="loadmore-mark"></div>
    <div
      v-show="loading || (list.length && isDone)"
      class="text-center py-32rpx text-hex-999 text-28rpx"
    >
      {{ isDone ? '已经到底了哦~' : loading ? '正在加载中...' : '' }}
    </div>
    <qh-safearea v-if="safe" />
  </div>
</template>

<script setup>
import isEqual from 'lodash/isEqual';
import { onMounted, onUnmounted, ref, watch, getCurrentInstance } from 'vue';
const props = defineProps({
  fn: {
    type: Function,
    default: () => () => [],
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  safe: Boolean,
  empty: {
    type: Boolean,
    default: true,
  },
});

let observer;
const page = ref(1);
const loading = ref(false);
const isDone = ref(false);

watch(
  () => props.params,
  (value, oldValue) => {
    if (!isEqual(value, oldValue)) refetch();
  }
);

const list = ref([]);

const refetch = async () => {
  if (page.value === 1 && loading.value) return;
  list.value = [];
  isDone.value = false;
  page.value = 1;
  await onFetch();
};

const useList = (fn) => {
  fn(list.value);
};

defineExpose({
  refetch,
  useList,
  list,
});

const emit = defineEmits(['change']);

const onFetch = async () => {
  const isReset = page.value === 1;
  loading.value = true;
  const data = await props.fn({
    pageSize: 10,
    ...props.params,
    pageIndex: page.value,
  });
  const { size, list: _list } = data;
  list.value = isReset ? _list : list.value.concat(_list);
  emit('change', { ...data, list: list.value });
  page.value++;
  isDone.value = size !== _list.length;
  loading.value = false;
};

onMounted(function () {
  observer = uni.createIntersectionObserver(getCurrentInstance()?.proxy);
  console.log(observer)
  observer
    .relativeToViewport({ bottom: 50 })
    .observe('.loadmore-mark', (res) => {
      !loading.value && !isDone.value && onFetch();
    });
});

onUnmounted(() => {
  observer.disconnect();
});
</script>

<style lang="scss" scoped></style>
