<template>
  <div class="text-hex-333" v-if="list.length">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="bg-white flex items-center justify-between h-144rpx px-32rpx py-32rpx"
    >
      <div class="flex flex-row items-center">
        <up-avatar
          shape="circle"
          size="50"
          :src="item.blackUserAvatar"
          customStyle="margin: -3px 5px -3px 0"
        ></up-avatar>
        <view class="ml-16rpx text-28rpx">{{ item.blackUserName }}</view>
      </div>
      <div class="w-120rpx">
        <up-button
          text="移除"
          :plain="true"
          shape="circle"
          iconColor="#0AD16D"
          @click="removeItem(item)"
        ></up-button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["change"]);

const removeItem = (item) => {
  emit("remove", { ...item });
};
</script>

<style lang="scss" scoped></style>
