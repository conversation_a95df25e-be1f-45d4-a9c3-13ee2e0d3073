import { ref, reactive } from "vue";
import { useLiveStore } from "@/store/liveStore.js";
import { storeToRefs } from "pinia";
export function usePlaying() {
  const liveStore = useLiveStore();
  const { zegoEngineInstance, zegoStreamID } = storeToRefs(liveStore);
  let palyOption = reactive({
    isPlayingStream: false,
    playBtnName: "Start Playing",
  });
  // 单击拉流操作
  async function onClickPlay() {
    console.log("-----------单击拉流操作11", this.userID);
    console.log("-----------单击拉流操作22", zegoStreamID.value);
    // if (palyOption.isPlayingStream) {
    //   stopPlayingStream(zegoStreamID.value);
    //   palyOption.playBtnName = "Start Playing";
    // } else {
    //   // await this.loginRoom(this.roomID, this.userID, this.userName);
    //   // if () {
    //   // }
    //   startPlayingStream(zegoStreamID.value);
    //   palyOption.playBtnName = "Stop Playing";
    // }
    // palyOption.isPlayingStream = !palyOption.isPlayingStream;
  }
  // 开始拉流
  async function startPlayingStream(streamID, config) {
    const result = await zegoEngineInstance.value.startPlayingStream(
      streamID,
      config
    );
    console.log(streamID, "--------------------------拉流成功");
    zegoEngineInstance.value.setPlayVolume(streamID, 150);
    return result;
  }

  // 停止拉流
  function stopPlayingStream(streamID) {
    zegoEngineInstance.value.stopPlayingStream(streamID);
  }

  return {
    palyOption,
    onClickPlay,
    startPlayingStream,
    stopPlayingStream,
  };
}
