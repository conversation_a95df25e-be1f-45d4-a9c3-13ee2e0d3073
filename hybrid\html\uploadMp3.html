<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />

    <title>上传音频文件</title>
  </head>
  <style>
    .box_main {
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    input[type="file"]::file-selector-button {
      width: 100px;
      height: 40px;
      font-size: 16px;
      border: 2px solid #0bd16e;
      border-radius: 6px;
      background-color: #0bd16e;
      transition: 1s;
      color: #fff;
    }

    input[type="file"]::file-selector-button:hover {
      background-color: #0bd16e;
      border: 2px solid #0bd16e;
    }
  </style>

  <body>
    <div class="box_main">
      <input type="file" id="audioFile" accept="audio/*" />
      <!-- 音频播放控件 -->
      <audio id="audioPlayer" controls style="display: none"></audio>
    </div>
  </body>
  <!-- uni 的 SDK -->
  <script
    type="text/javascript"
    src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"
  ></script>
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script type="text/javascript">
    document.addEventListener("UniAppJSBridgeReady", function () {
      uni.webView.getEnv(function (res) {
        console.log("---------当前环境：" + JSON.stringify(res));
      });
      document
        .getElementById("audioFile")
        .addEventListener("change", function (event) {
          var file = event.target.files[0]; // 获取选择的文件
          var audioPlayer = document.getElementById("audioPlayer"); // 获取音频播放控件
          if (file) {
            // 检查文件类型是否为音频
            console.log(`
            上传附件:${file.name} 附件大小:${file.size}\n
            请求头:${JSON.stringify(file)}\n
            `);
            if (file.type.match("audio.*")) {
              audioPlayer.src = URL.createObjectURL(file);
              // 显示音频播放控件
              audioPlayer.style.display = "block";
            } else {
              alert("请选择音频文件！");
            }
            console.log("---------上传参数", file);
            try {
              axios
                .post(
                  "http://101.34.5.141:8011/tts/audio/upload/11139",
                  {
                    file: file,
                  },
                  {
                    headers: {
                      token:
                        "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpbklkIjoie1wiYXBwVXNlclwiOjIsXCJzdXBlckFkbWluRmxhZ1wiOmZhbHNlLFwiYXZhdGFyXCI6XCJodHRwczovL21lbmd0dS0xMzA1NDY4MjM1LmNvcy5hcC1zaGFuZ2hhaS5teXFjbG91ZC5jb20vY29taWMvMjAyNC0wNy0wNC9mM2Y3ZGNlYi1iOTMwLTQ3MzUtYjNlNS1mMzM3MWMwZmYyY2MuanBnXCIsXCJkZWxGbGFnXCI6MCxcImlzTnVtYmVyXCI6MCxcInRlbmFudElkXCI6MCxcImlkXCI6MjAsXCJ1c2VybmFtZVwiOlwi5bCP6ams6YeM5aWlODg4XCIsXCJzdGF0dXNcIjowfSIsInJuIjoiUzBVSURsV2dxWk9UNlVLSGlDcXZzbUNYc2xMNEl2UEYifQ.NFZBSmQISuwKMtMRDv1HUaw9lMO9VNpExhsHE4mMV30",
                    },
                  }
                )
                .then(function (res) {
                  const fileData = JSON.parse(res);
                  console.log("---------录音上传成功", fileData);
                })
                .catch(function (error) {
                  console.log("----上传axios报错", error);
                });
            } catch (error) {
              console.error("公共图片及logo", error);
            }
          }
        });
      // uni.webView.navigateTo(...)
    });
  </script>
</html>
