# nh-authorize

## 使用方法

1. 引入插件，如nuve为例子：
```javascript
import {
	requestPermissionall,
	requestPermission
} from '@/utils/nh-authorize.js'
```

2.多权限同时请求：

```javascript
requestPermissionall(['camera','record']).then(value => {
	//权限没有通过会false并请求授权弹窗或授权设置页
	//通过后直接执行
			if(value){
				//您的业务代码
			}
		})
```


3. 单权限请求：

```javascript
requestPermissionall('camera').then(value => {
	//权限没有通过会false并请求授权弹窗或授权设置页
	//通过后直接执行
			if(value){
				//您的业务代码
			}
		})
```


## 支持权限


| scope   | 说明                                      | APP |   微信小程序 |
| ------- |  --------------------------------------- | ----- |-----------|
| location| 定位                                      | 支持   |    支持    |
| camera  | 摄像头                                    | 支持   |    支持    |
| record  | 麦克风                                    | 支持   |    支持    |
| photo   | 相册                                      | 支持   |    支持    |
| contact | 通讯录                                    | 支持   |    不支持   |
| calendar| 日历                                      | 支持   |    不支持   |
| push    | 推送消息（安卓不支持）                       | 支持   |    不支持   |
| memo    | 备忘录 （安卓不支持）                        | 支持   |    不支持   |
| userInfo| 用户信息                                   | 不支持 |    支持    |
| userLocationBackground| 后台定位                     | 不支持  |    支持   |
| address| 地址                                       | 不支持  |    支持   |
| invoice| 发票信息                                    | 不支持  |    支持   |
| invoiceTitle| 发票抬头                                | 不支持  |    支持   |
| werun  | 微信运动步数                                  | 不支持  |    支持   |
| invoice| 发票信息                                     | 不支持  |    支持   |

如有bug请联系QQ1718288989

⚠️注意：本模块封装基于《App权限判断和提示》的模块进行二次升级封装，意在更方便使用，尊重原创，向之前模块作者致敬！
