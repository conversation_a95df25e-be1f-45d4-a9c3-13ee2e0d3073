// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export function sendImageMessage(zim, log) {
	
	uni.chooseImage({
		count: 1,
		success: res => {
			const file = res.tempFiles[0];
			console.log('file', file);
			const localPath = plus.io.convertLocalFileSystemURL(file.path);
			
			zim.sendMediaMessage({ type: 11, fileLocalPath: localPath }, 'tg002', 0, config, notification)
			    .then(log.bind(null, 'sendMediaMessage'))
			    .catch(log.bind(null, 'sendMediaMessage'));
		},
		fail: () => {
			log.bind(null, 'chooseImage:error');
		}
	})
}

export function sendVideoMessage(zim, log) {
	uni.chooseVideo({
		count: 1,
		success: res => {
			const localPath = plus.io.convertLocalFileSystemURL(res.tempFilePath);
			
			zim.sendMediaMessage({ type: 11, fileLocalPath: localPath }, 'tg002', 0, config, notification)
			    .then(log.bind(null, 'sendMediaMessage'))
			    .catch(log.bind(null, 'sendMediaMessage'));
		},
		fail: () => {
			log.bind(null, 'chooseVideo:error');
		}
	});
}
