import { liveroomCloseRoom, liveroomExitRoom } from "@/services/liveSetting";
import { ref } from "vue";
// #ifdef APP-PLUS
import { useZegoEngin } from "@/hooks/useZegoEngin";
import { useZegoIm } from "@/hooks/useZegoIm";
// #endif
import { useLiveStore } from "@/store/liveStore.js";
import { storeToRefs } from "pinia";
// import {useR}

export function useCloseLive() {
  const liveStore = useLiveStore();
  // #ifdef APP-PLUS
  const { destroyEngine } = useZegoEngin();
  const { leaveRoom } = useZegoIm(liveStore.sendSuccessCallback);
  // #endif

  let { roomInfo, isShowSuspend, zegoEngineInstance, haveEngine } =
    storeToRefs(liveStore);

  const showNoticeModal = ref(false);

  async function closeLiveHome(islive) {
    console.log("------------------zxdzx关闭直播间", roomInfo.value?.isAnchor);
    liveStore.resetLiveCall();
    if (roomInfo.value?.isAnchor) {
      // await closeRoom() // 开播用户 退出直播间时关闭直播
      showNoticeModal.value = true;
      return;
    } else {
      console.log("普通用户退出,关闭直播", roomInfo.value);
      await exitRoom(); // 退出直播
      await leaveRoom(roomInfo.value?.roomID, roomInfo.value);
      destroyEngine(); // destroyEngine中销毁直播引擎和退出IM聊天房间
      liveStore.resetRoom();
      if (islive) {
        // 在直播间点击关闭 销毁之后后 回到直播列表页面
        uni.switchTab({
          url: "/pages/live/livelist/index",
        });
      }
    }
  }
  // 关闭直播间
  async function closeRoom() {
    let data = await liveroomCloseRoom(roomInfo.value.roomID);
    return data;
  }
  // 退出直播间
  async function exitRoom() {
    let data = await liveroomExitRoom(roomInfo.value.roomID);
    return data;
  }
  // 弹窗提示后关闭直播间，销毁引擎
  async function confirmNoticeModal(isexit) {
    let result = await closeRoom();
    console.log("----------------主播,关闭直播", result);
    liveStore.setLiveCountData(result);
    showNoticeModal.value = false;
    // leaveRoom(roomInfo.value?.roomID, roomInfo.value);
    // destroyEngine中销毁直播引擎和退出IM聊天房间
    destroyEngine();
    if (roomInfo.value?.isAnchor) {
      liveStore.resetRoom();
      uni.redirectTo({
        url: "/pages/liveCount/index",
      });
    }
  }

  // 在直播列表页面切换直播间 快速切换 不需要弹窗提示
  async function quickCloseLive() {
    let result = await exitRoom();
    // await leaveRoom(roomInfo.value?.roomID, roomInfo.value);
    // destroyEngine中销毁直播引擎和退出IM聊天房间
    destroyEngine();
    liveStore.resetRoom();
    return result;
  }

  function cancelNoticeModal() {
    showNoticeModal.value = false;
  }

  return {
    closeLiveHome,
    showNoticeModal,
    confirmNoticeModal,
    cancelNoticeModal,
    quickCloseLive,
  };
}
