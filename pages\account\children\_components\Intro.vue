<template>
  <div>
    <image src="@/static/img/account/img-child.png" class="w-full h-380rpx" />
    <div class="text-28rpx leading-52rpx text-hex-999 mt-48rpx w-542rpx mx-auto">
      为呵护未成年人健康成长，氢次元推出青少年守护模式，该模式下会精选出适合青少年阅读的内容。为青少年提供安全良好的阅读体验。
    </div>
    <div class="fixed py-24rpx bottom-0 w-full bg-white">
      <div class="text-hex-999 text-28rpx text-center leading-40rpx mb-32rpx" v-if="opened" @click="emit('modify')">修改密码
      </div>
      <div class="h-88rpx mx-32rpx rounded-[48rpx] bg-primary text-white flex items-center justify-center text-32rpx"
        @click="emit('next')">
        {{ opened ? '退出青少年模式' : '下一步' }}
      </div>
      <qh-safearea />
    </div>
</div>
</template>

<script setup>

defineProps({
  opened: Boolean
})

const emit = defineEmits(['next', 'modify'])
</script>

<style lang="scss" scoped></style>