<template>
  <div class="relative" v-bind="$attrs">
    <div class="absolute inset-0 -z-1">
      <image :src="img" mode="aspectFill" class="w-full h-full" />
      <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-filter backdrop-blur-xl"></div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    img: {
      type: String,
      default: ''
    }
  }
}
</script>
