import {ref,reactive} from 'vue'
import {
	useLiveStore
} from '@/store/liveStore.js'
import {
	storeToRefs
} from "pinia";

export function useOnMessage() {
	const liveStore = useLiveStore()
	const {
		zegoEngineInstance,
		zegoStreamID,
		roomInfo,
	} = storeToRefs(liveStore)
	// 监听聊天信息
	function addListeners(callback) {
		 zegoEngineInstance.value.on("IMRecvBarrageMessage", (roomID, messageList) => {
			 console.log(`IMRecvBarrageMessage: roomID:${roomID}, messageList:${JSON.stringify(
		             messageList
		         )}`)
		     
		     let info = messageList[messageList.length - 1];
			 console.log(info)
			 console.log(info.message)
			   // console.log(JSON.parse(info.message))
			   callback({message: info.message,roomID: roomID})
			   // this.$emit('onBarrageMessage', {message: info.message,roomID: roomID})
		 });
	 }
	 // 发送聊天内容
	async function sendBarrageMsg(sendMessage, type) {
		console.log('点击确定发送弹幕', sendMessage, type)
		let sendMes = {
			userID: roomInfo.value.userID,
			userName: roomInfo.value.userName,
			avatar: roomInfo.value.avatar,
			isAnchor: roomInfo.value.isAnchor,
			type,
			}
			if(type === 'message') {
				sendMes.sendMessage = sendMessage
			} else if(type === 'gift') {
				let info = JSON.parse(sendMessage)
				sendMes.sendMessage = ''
				sendMes.giftId = info.giftId
				sendMes.giftNum = info.giftNum
				
			} else if(type==='addUser'){
				// 
				console.log('用户登录++++++++++++adduser+++++++++发送弹幕信息')
				let info = JSON.parse(sendMessage)
				sendMes = {
					...info,
					type
				}
			} else{
				console.log('用户登录=============deleteUser==========发送弹幕信息')
				let info = JSON.parse(sendMessage)
				sendMes = {
					...info,
					type
				}
			}
	
			const messInfo = JSON.stringify(sendMes)
			console.log(JSON.stringify(messInfo))
	    let result = await zegoEngineInstance.value.sendBarrageMessage(
	        roomInfo.value.roomID,
			messInfo
	        
	    );
			 console.log('发送弹幕成功1111')
		 // this.$emit('onBarrageMessage', {message: messInfo})
	   return { message: messInfo }
	}
	
	return {
		addListeners,
		sendBarrageMsg
	}
}