const ZIMNativeModule = uni.requireNativePlugin('zego-ZIMUniPlugin_ZIMUniEngine');
export class ZIMLogger {
    warn(tag, action, msg) {
        const log = msg && typeof msg != 'string' ? this.stringify(msg) : msg || '';
        if (log) {
            console.log(action, log);
            tag && ZIMNativeModule.callMethod("writeCustomLog", { customLog: log, moduleName: action });
        }
    }
    /**
     * Format the log to reduce a large number of invalid logs
     *
     * 1. Uint8Array           ->  b=length
     * 2. Array.length > 2     ->  [length, Array[first], Array[last]]
     * 3. String.length > 128  ->  s=length
     *
     */
    stringify(obj) {
        const str = JSON.stringify(obj, (key, value) => {
            if (value instanceof Uint8Array)
                return 'b=' + value.length;
            if (value instanceof Array && value.length > 2) {
                const len = value.length;
                return [len, value[0], value[len - 1]];
            }
            if (typeof value == 'string' && value.length > 128)
                return 's=' + value.length;
            return value;
        });
        return str.replace(/\"(\w+)\":/g, '$1:');
    }
}
export var ZIMLogTag;
(function (ZIMLogTag) {
    ZIMLogTag["Manager"] = "MGR";
    ZIMLogTag["Connection"] = "Conn";
    ZIMLogTag["Database"] = "DB";
    ZIMLogTag["User"] = "User";
    ZIMLogTag["Conversation"] = "Conv";
    ZIMLogTag["Strategy"] = "ST";
    ZIMLogTag["Room"] = "Room";
    ZIMLogTag["Group"] = "Group";
    ZIMLogTag["Call"] = "Call";
    ZIMLogTag["Friend"] = "Friend";
})(ZIMLogTag || (ZIMLogTag = {}));
export var ZIMLogAction;
(function (ZIMLogAction) {
    // API - Main
    ZIMLogAction["CreateEngine"] = "API.createEngine";
    ZIMLogAction["DestroyEngine"] = "API.destroyEngine";
    ZIMLogAction["SetLogConfig"] = "API.setLogConfig";
    ZIMLogAction["UploadLog"] = "API.uploadLog";
    ZIMLogAction["Login"] = "API.login";
    ZIMLogAction["Logout"] = "API.logout";
    ZIMLogAction["RenewToken"] = "API.renewToken";
    // API - User
    ZIMLogAction["QueryUsersInfo"] = "API.queryUsersInfo";
    ZIMLogAction["UpdateUserName"] = "API.updateUserName";
    ZIMLogAction["UpdateUserAvatarUrl"] = "API.updateUserAvatarUrl";
    ZIMLogAction["UpdateUserExtendedData"] = "API.updateUserExtendedData";
    ZIMLogAction["UpdateUserOfflinePushRule"] = "API.updateUserOfflinePushRule";
    ZIMLogAction["QuerySelfUserRule"] = "API.querySelfUserRule";
    // API - Conversation
    ZIMLogAction["QueryConversation"] = "API.queryConversation";
    ZIMLogAction["QueryConversationList"] = "API.queryConversationList";
    ZIMLogAction["QueryPinnedList"] = "API.queryConversationPinnedList";
    ZIMLogAction["DeleteConversation"] = "API.deleteConversation";
    ZIMLogAction["ClearUnreadMessageCount"] = "API.clearConversationUnreadMessageCount";
    ZIMLogAction["SetNotificationStatus"] = "API.setConversationNotificationStatus";
    ZIMLogAction["UpdatePinnedState"] = "API.updateConversationPinnedState";
    ZIMLogAction["SetConvDraft"] = "API.setConversationDraft";
    // API - Message
    ZIMLogAction["SendMessage"] = "API.sendMessage";
    ZIMLogAction["SendMediaMessage"] = "API.sendMediaMessage";
    ZIMLogAction["DeleteMessages"] = "API.deleteMessages";
    ZIMLogAction["DeleteAllMessage"] = "API.deleteAllMessage";
    ZIMLogAction["QueryHistoryMessage"] = "API.queryHistoryMessage";
    ZIMLogAction["InsertMessageToLocalDB"] = "API.insertMessageToLocalDB";
    ZIMLogAction["UpdateMessageLocalExtendedData"] = "API.updateMessageLocalExtendedData";
    ZIMLogAction["SendReceiptRead"] = "API.sendConversationMessageReceiptRead";
    ZIMLogAction["SendMessageReceiptsRead"] = "API.sendMessageReceiptsRead";
    ZIMLogAction["QueryReceiptsInfo"] = "API.queryMessageReceiptsInfo";
    ZIMLogAction["QueryReceiptReadMemberList"] = "API.queryGroupMessageReceiptReadMemberList";
    ZIMLogAction["QueryReceiptUnreadMemberList"] = "API.queryGroupMessageReceiptUnreadMemberList";
    ZIMLogAction["RevokeMessage"] = "API.RevokeMessage";
    ZIMLogAction["QueryCombineMessage"] = "API.queryCombineMessageDetail";
    // API - Message reaction
    ZIMLogAction["AddReaction"] = "JSAPI.addMessageReaction";
    ZIMLogAction["DeleteReaction"] = "JSAPI.deleteMessageReaction";
    ZIMLogAction["QueryReaction"] = "JSAPI.queryMessageReactionUserList";
    // API - Room
    ZIMLogAction["CreateRoom"] = "API.createRoom";
    ZIMLogAction["JoinRoom"] = "API.joinRoom";
    ZIMLogAction["LeaveRoom"] = "API.leaveRoom";
    ZIMLogAction["QueryRoomMemberList"] = "API.queryRoomMemberList";
    ZIMLogAction["QueryRoomMembers"] = "API.queryRoomMembers";
    ZIMLogAction["QueryRoomOnlineMemberCount"] = "API.queryRoomOnlineMemberCount";
    ZIMLogAction["SetRoomAttributes"] = "API.setRoomAttributes";
    ZIMLogAction["DeleteRoomAttributes"] = "API.deleteRoomAttributes";
    ZIMLogAction["QueryRoomAllAttributes"] = "API.queryRoomAllAttributes";
    ZIMLogAction["BeginRoomAttributesBatchOperation"] = "API.beginRoomAttributesBatchOperation";
    ZIMLogAction["EndRoomAttributesBatchOperation"] = "API.endRoomAttributesBatchOperation";
    ZIMLogAction["SetRoomMembersAttributes"] = "API.setRoomMembersAttributes";
    ZIMLogAction["QueryRoomMembersAttributes"] = "API.queryRoomMembersAttributes";
    ZIMLogAction["QueryRoomMemberAttributesList"] = "API.queryRoomMemberAttributesList";
    // API - Group
    ZIMLogAction["CreateGroup"] = "API.createGroup";
    ZIMLogAction["EnterRoom"] = "API.enterRoom";
    ZIMLogAction["JoinGroup"] = "API.joinGroup";
    ZIMLogAction["DismissGroup"] = "API.dismissGroup";
    ZIMLogAction["LeaveGroup"] = "API.leaveGroup";
    ZIMLogAction["InviteUsersIntoGroup"] = "API.inviteUsersIntoGroup";
    ZIMLogAction["KickGroupMembers"] = "API.kickGroupMembers";
    ZIMLogAction["QueryGroupList"] = "API.queryGroupList";
    ZIMLogAction["QueryGroupMemberList"] = "API.queryGroupMemberList";
    ZIMLogAction["QueryGroupMemberCount"] = "API.queryGroupMemberCount";
    ZIMLogAction["TransferGroupOwner"] = "API.transferGroupOwner";
    ZIMLogAction["QueryGroupInfo"] = "API.queryGroupInfo";
    ZIMLogAction["UpdateGroupName"] = "API.updateGroupName";
    ZIMLogAction["UpdateGroupNotice"] = "API.updateGroupNotice";
    ZIMLogAction["UpdateGroupAvatarUrl"] = "API.updateGroupAvatarUrl";
    ZIMLogAction["SetGroupAttributes"] = "API.setGroupAttributes";
    ZIMLogAction["DeleteGroupAttributes"] = "API.deleteGroupAttributes";
    ZIMLogAction["QueryGroupAttributes"] = "API.queryGroupAttributes";
    ZIMLogAction["SetGroupMemberNickname"] = "API.setGroupMemberNickname";
    ZIMLogAction["SetGroupMemberRole"] = "API.setGroupMemberRole";
    ZIMLogAction["QueryGroupMemberInfo"] = "API.queryGroupMemberInfo";
    ZIMLogAction["MuteGroup"] = "API.muteGroup";
    ZIMLogAction["MuteGroupMembers"] = "API.muteGroupMembers";
    ZIMLogAction["QueryGroupApp"] = "API.queryGroupApplicationList";
    ZIMLogAction["UpdateGroupVerifyMode"] = "API.updateGroupVerifyMode";
    // API - Call
    ZIMLogAction["CallInvite"] = "API.callInvite";
    ZIMLogAction["CallCancel"] = "API.callCancel";
    ZIMLogAction["CallAccept"] = "API.callAccept";
    ZIMLogAction["CallReject"] = "API.callReject";
    ZIMLogAction["CallJoin"] = "API.callJoin";
    ZIMLogAction["CallQuit"] = "API.callQuit";
    ZIMLogAction["CallEnd"] = "API.callEnd";
    ZIMLogAction["CallingInvite"] = "API.callingInvite";
    ZIMLogAction["QueryCallList"] = "API.queryCallInvitationList";
    // API - Friend
    ZIMLogAction["AddFriend"] = "API.addFriend";
    ZIMLogAction["SendFriendApp"] = "API.sendFriendApplication";
    ZIMLogAction["DeleteFriends"] = "API.deleteFriends";
    ZIMLogAction["CheckFriends"] = "API.checkFriendsRelation";
    ZIMLogAction["UpdateFriendAlias"] = "API.updateFriendAlias";
    ZIMLogAction["UpdateFriendAttr"] = "API.updateFriendAttributes";
    ZIMLogAction["AcceptFriendApp"] = "API.acceptFriendApplication";
    ZIMLogAction["RejectFriendApp"] = "API.rejectFriendApplication";
    ZIMLogAction["QueryFriendsInfo"] = "API.queryFriendsInfo";
    ZIMLogAction["QueryFriendList"] = "API.queryFriendList";
    ZIMLogAction["QueryFriendAppList"] = "API.queryFriendApplicationList";
    // API - Blacklist
    ZIMLogAction["AddUserToBlacklist"] = "API.addUsersToBlacklist";
    ZIMLogAction["RemoveUsersFromBlacklist"] = "API.removeUsersFromBlacklist";
    ZIMLogAction["CheckUserIsInBlacklist"] = "API.checkUserIsInBlacklist";
    ZIMLogAction["QueryBlacklist"] = "API.queryBlacklist";
    // API - DB Search
    ZIMLogAction["SearchConversations"] = "API.searchLocalConversations";
    ZIMLogAction["SearchGlobalMessages"] = "API.searchGlobalLocalMessages";
    ZIMLogAction["SearchMessages"] = "API.searchLocalMessages";
    ZIMLogAction["SearchGroups"] = "API.searchLocalGroups";
    ZIMLogAction["SearchGroupMembers"] = "API.searchLocalGroupMembers";
    ZIMLogAction["SearchFriends"] = "API.searchLocalFriends";
    // Connection
    ZIMLogAction["SendCMD"] = "SCMD";
    ZIMLogAction["PushCMD"] = "PCMD";
    ZIMLogAction["Reconnection"] = "reconn";
    ZIMLogAction["OpenConnection"] = "openconn";
    ZIMLogAction["CloseConnection"] = "closeconn";
    // Database
    ZIMLogAction["CreateDB"] = "createDB";
    ZIMLogAction["DeleteDB"] = "deleteDB";
    ZIMLogAction["WriteDB"] = "writeDB";
})(ZIMLogAction || (ZIMLogAction = {}));
