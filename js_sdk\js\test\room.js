// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export async function useRoomModule(log, time) {
    const zim = ZIM.getInstance();
    const roomID = 'rtg-' + time;

    try {
        let res = await zim.createRoom(
            { roomID, roomName: roomID },
            { roomAttributes: { q: 'a', w: '1' }, roomDestroyDelayTime: 10 },
        );
        log('createRoom', res);

        res = await zim.enterRoom({ roomID: roomID + 1, roomName: roomID + 1 }, { roomAttributes: { e: 'a', r: '1' } });
        log('enterRoom', res);
    } catch (error) {
        log('createRoom', error);
        throw error;
    }

    zim.joinRoom('rtg001').then(log.bind(null, 'joinRoom')).catch(log.bind(null, 'joinRoom'));
    zim.queryRoomMemberList(roomID, { count: 10, nextFlag: '' })
        .then(log.bind(null, 'queryRoomMemberList'))
        .catch(log.bind(null, 'queryRoomMemberList'));
    zim.queryRoomOnlineMemberCount(roomID)
        .then(log.bind(null, 'queryRoomOnlineMemberCount'))
        .catch(log.bind(null, 'queryRoomOnlineMemberCount'));

    // Room Attributes
    const attrConfig = { isForce: false, isUpdateOwner: false, isDeleteAfterOwnerLeft: false };
    zim.queryRoomAllAttributes(roomID)
        .then(log.bind(null, 'queryRoomAllAttributes1'))
        .catch(log.bind(null, 'queryRoomAllAttributes1'));
    zim.beginRoomAttributesBatchOperation(roomID, attrConfig);
    zim.setRoomAttributes({ e: 'aa', r: '2' }, roomID, attrConfig)
        .then(log.bind(null, 'setRoomAttributes'))
        .catch(log.bind(null, 'setRoomAttributes'));
    zim.deleteRoomAttributes(['r'], roomID, attrConfig)
        .then(log.bind(null, 'deleteRoomAttributes'))
        .catch(log.bind(null, 'deleteRoomAttributes'));
    zim.endRoomAttributesBatchOperation(roomID)
        .then(log.bind(null, 'endRoomAttributesBatchOperation'))
        .catch(log.bind(null, 'endRoomAttributesBatchOperation'));
    zim.queryRoomAllAttributes(roomID)
        .then(log.bind(null, 'queryRoomAllAttributes2'))
        .catch(log.bind(null, 'queryRoomAllAttributes2'));

    // Members Attributes
    try {
        const res = await zim.setRoomMembersAttributes({ a: 'a', b: '1' }, ['tg001', 'tg002'], roomID, attrConfig);
        log('setRoomMembersAttributes', res);
    } catch (error) {
        log('setRoomMembersAttributes', error);
    }
    zim.queryRoomMembersAttributes(['tg001', 'tg002'], roomID)
        .then(log.bind(null, 'queryRoomMembersAttributes'))
        .catch(log.bind(null, 'queryRoomMembersAttributes'));
    zim.queryRoomMemberAttributesList(roomID, { nextFlag: '', count: 10 })
        .then(log.bind(null, 'queryRoomMemberAttributesList'))
        .catch(log.bind(null, 'queryRoomMemberAttributesList'));

    zim.leaveRoom(roomID).then(log.bind(null, 'leaveRoom')).catch(log.bind(null, 'leaveRoom'));
}
