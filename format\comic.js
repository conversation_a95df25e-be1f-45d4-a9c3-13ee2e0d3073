import dayjs from "dayjs";
import { page } from "../utils/request";

const SECONDS_PER_DAY = 60 * 60 * 24;

export function fmtLastRead(lastReadTime) {
  let lastReadLabel = "";
  if (lastReadTime) {
    const now = dayjs();
    const lastRead = dayjs(lastReadTime);
    const startOfToday = now.startOf("date");
    if (lastRead > startOfToday) {
      lastReadLabel = "今天读过";
    } else {
      const lastReadSecBeforeNow = startOfToday.unix() - lastRead.unix();
      if (lastReadSecBeforeNow < SECONDS_PER_DAY) {
        lastReadLabel = "昨天读过";
      } else {
        lastReadLabel = `${Math.ceil(
          lastReadSecBeforeNow / SECONDS_PER_DAY
        )}天前读过`;
      }
    }
  }
  return lastReadLabel;
}

export function fmtComic(item) {
  const {
    id,
    cartoonId,
    title,
    headline: subtitle,
    intro: desc,
    coverList,
    coverDetail,
    coverSpecialArea: coverSection,
    coverFirstPage: coverHomepage,
    labelIds: tags,
    themeIds: categories,
    fullFlag,
    hotValue: heat,
    currentChapter,
    chapters,
    lastReadTime,
    statusFavorite = 1,
    statusHistory = 1,
    statusPraise = 1,
    praiseNum,
    isFree = 0,
    ...rest
  } = item;
  const lastReadLabel = fmtLastRead(lastReadTime);

  return {
    id: cartoonId || id,
    _id: id,
    title,
    subtitle,
    desc,
    tags,
    categories,
    coverList,
    coverDetail,
    coverSection,
    coverHomepage,
    isFinish: fullFlag === 0,
    heat,
    currentChapter: Math.min(currentChapter, chapters),
    chapters,
    isLiked: statusPraise === 0,
    isFollow: statusFavorite === 0,
    lastReadLabel,
    likes: praiseNum,
    isFree,
    // TODO
    comments: 0,
    ...rest,
  };
}

export function fmtComics(list) {
  return list.map(fmtComic);
}

export async function pageComics(
  url,
  pageIndex,
  size = 10,
  filter = {},
  sort = {}
) {
  const data = await page(url, pageIndex, size, filter, sort);
  data.list = fmtComics(data.list);
  return data;
}
