// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export async function useGroupModule(log, time) {
    const zim = ZIM.getInstance();
    const groupID = 'gtg-' + time;

    try {
        let res = await zim.createGroup({ groupID, groupName: groupID, groupAvatarUrl: groupID }, ['tg002', 'tg003'], {
            groupAttributes: { a: 'a', b: '1' },
            groupNotice: groupID,
        });
        log('createGroup', res);
        const _groupID = 1 + groupID;
        res = await zim.createGroup({ groupID: _groupID, groupName: _groupID, groupAvatarUrl: _groupID }, ['tg002']);
        log('createGroup2', res);
        res = await zim.joinGroup('gtg001');
        log('joinGroup', res);
    } catch (error) {
        log('createGroup', error);
        throw error;
    }

    zim.transferGroupOwner('tg002', 1 + groupID)
        .then(log.bind(null, 'transferGroupOwner'))
        .catch(log.bind(null, 'transferGroupOwner'));
    zim.queryGroupList().then(log.bind(null, 'queryGroupList')).catch(log.bind(null, 'queryGroupList'));

    try {
        let res = await zim.updateGroupName(groupID + '-name', groupID);
        log('updateGroupName', res);
        res = await zim.updateGroupNotice(groupID + '-notice', groupID);
        log('updateGroupNotice', res);
        res = await zim.updateGroupAvatarUrl(groupID + '-avatar', groupID);
        log('updateGroupAvatarUrl', res);
        zim.queryGroupInfo(groupID).then(log.bind(null, 'queryGroupInfo')).catch(log.bind(null, 'queryGroupInfo'));
    } catch (error) {
        log('updateGroupName', error);
    }

    // Group Attributes
    try {
        let res = await zim.setGroupAttributes({ b: 'aa', c: '2' }, groupID);
        log('setGroupAttributes', res);
        res = await zim.deleteGroupAttributes(['a'], groupID);
        log('deleteGroupAttributes', res);
        zim.queryGroupAllAttributes(groupID)
            .then(log.bind(null, 'queryGroupAllAttributes'))
            .catch(log.bind(null, 'queryGroupAllAttributes'));
        zim.queryGroupAttributes(['a', 'b', 'c'], groupID)
            .then(log.bind(null, 'queryGroupAttributes'))
            .catch(log.bind(null, 'queryGroupAttributes'));
    } catch (error) {
        log('setGroupAttributes', error);
    }

    // Members
    try {
        let res = await zim.setGroupMemberNickname('tg003-nick', 'tg003', groupID);
        log('setGroupMemberNickname', res);
        res = await zim.setGroupMemberRole(5, 'tg003', groupID);
        log('setGroupMemberRole', res);
        zim.queryGroupMemberInfo('tg003', groupID)
            .then(log.bind(null, 'queryGroupMemberInfo'))
            .catch(log.bind(null, 'queryGroupMemberInfo'));
        res = await zim.inviteUsersIntoGroup(['tg004'], groupID);
        log('inviteUsersIntoGroup', res);
        res = await zim.kickGroupMembers(['tg002'], groupID);
        log('kickGroupMembers', res);
        zim.queryGroupMemberList(groupID, { nextFlag: 0, count: 10 })
            .then(log.bind(null, 'queryGroupMemberList'))
            .catch(log.bind(null, 'queryGroupMemberList'));
        zim.queryGroupMemberCount(groupID)
            .then(log.bind(null, 'queryGroupMemberCount'))
            .catch(log.bind(null, 'queryGroupMemberCount'));
    } catch (error) {
        log('setGroupMemberNickname', error);
    }

    zim.leaveGroup('gtg001').then(log.bind(null, 'leaveGroup')).catch(log.bind(null, 'leaveGroup'));
    zim.dismissGroup(groupID).then(log.bind(null, 'dismissGroup')).catch(log.bind(null, 'dismissGroup'));
}
