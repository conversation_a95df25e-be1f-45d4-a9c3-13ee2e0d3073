<template>
  <div class="page bg-hex-f5f5f5">
    <qh-navbar title="朋友权限" center offset background="#f5f5f5" dark />
    <view>
      <view
        class="mt-24rpx ml-32rpx mr-32rpx h-96rpx bg-white radius-item flex flex-row items-center justify-between pl-32rpx pr-32rpx"
      >
        <view>允许跟房</view>
        <up-switch
          v-model="followRoomValue"
          activeColor="#0AD16D"
          @change="followRoom"
        ></up-switch>
      </view>

      <Item
        class="mt-24rpx ml-32rpx mr-32rpx h-96rpx bg-white radius-item pl-32rpx pr-32rpx"
        text="通讯录黑名单"
        @click="onLink('account/friendPermission/blackList')"
      />
    </view>
  </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import Item from "./../setting/_components/Item.vue";
import { updateUser } from "@/services/account";
import { showToast } from "@/utils/utils";

const IS_DEV = import.meta.env.DEV;

onLoad(async () => {});

const userStore = useUserStore();

const followRoomValue = ref(userStore.shareRoom == 1);

const followRoom = (e) => {
  console.log(followRoomValue.value);
  updateUser(followRoomValue.value ? "1" : "0")
    .then((res) => {
      userStore.setUserInfo({ shareRoom: followRoomValue.value });
      showToast("操作成功");
    })
    .catch(() => {
      showToast("操作失败");
      followRoomValue.value = !followRoomValue.value;
    });
};

const onLink = uni.routeTo;
</script>

<style lang="scss" scoped>
.radius-item {
  border-radius: 16rpx;
}
</style>
