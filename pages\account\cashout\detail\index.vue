<template>
  <view>
    <qh-navbar title="水分子明细" center offset background="white" dark />
    <view class="bg-hex-F5F5F5 w-100vw h-20rpx"></view>
    <view class="mt-0rpx w-100vw h-160rpx">
      <view class="flex flex-row justify-between ml-32rpx mt-32rpx mr-32rpx">
        <view class="flex flex-row items-center" @click="clickDate">
          <view class="">{{ chooseDateString() }}</view>
          <image
            class="w-24rpx h-24rpx ml-12rpx"
            :src="dateActive ? pullUp : pullDown"
          />
        </view>
        <view class="flex flex-row items-center" @click="clickFilter">
          <span>{{ filterItemData.text }}</span>
          <image
            class="w-24rpx h-24rpx ml-12rpx"
            :src="filterActive ? pullUp : pullDown"
          />
        </view>
      </view>
      <view class="ml-32rpx mt-32rpx flex flex-row">
        <span class="text-hex-999999">{{ "支出¥" + exchangeH2o }}</span>
        <span class="text-hex-999999 ml-16rpx">{{
          "收入¥" + rechangeH2o
        }}</span>
      </view>
    </view>
    <view class="bg-hex-F5F5F5 w-100vw h-20rpx"></view>
    <view class="top-400rpx w-100vw fixed bottom-0">
      <up-list class="list" @scrolltolower="scrolltolower">
        <up-list-item v-for="(item, index) in indexList" :key="index">
          <view class="flex flex-row justify-between items-center h-160rpx">
            <view class="ml-32rpx">
              <view class="text-hex-333 text-36rpx">{{
                item.transactionType
              }}</view>
              <view class="mt-16rpx text-hex-999 text-30rpx">{{
                item.eventTime
              }}</view>
            </view>
            <view
              class="mr-32rpx text-48rpx font-bold text-hex-FF6767"
              :class="
                parseInt(item.account + '') > 0
                  ? 'text-hex-FF6767'
                  : 'text-hex-0AD16D'
              "
              >{{ item.account }}</view
            >
          </view>
          <view class="ml-32rpx mr-32rpx h-1rpx bg-hex-f5f5f5"></view>
        </up-list-item>
      </up-list>
    </view>
    <!-- <up-datetime-picker :show="dateActive" v-model="filterDateData" mode="date" :minDate="-*************"
			@confirm="dateConfirm" @cancel="dateActive = false"
			@close="dateActive = false"></up-datetime-picker> -->
    <up-popup :show="filterActive">
      <view>
        <view
          class="w-100vw h-112rpx flex items-center justify-center relative"
        >
          <view class="text-36rpx">全部</view>
          <view
            class="absolute right-0rpx top-0 w-100rpx h-112rpx flex items-center justify-center"
            @click="closeFilter"
          >
            <image class="w-28rpx h-28rpx" :src="close" />
          </view>
        </view>
        <up-grid :border="false" col="3" @click="click">
          <up-grid-item
            v-for="(item, index) in filterList"
            :key="index"
            @click="filterItemClick(item, index)"
          >
            <view
              class="flex items-center justify-center"
              :class="
                clickIndex == index ? 'filter-item-active' : 'filter-item'
              "
            >
              <text>{{ item.text }}</text>
            </view>
          </up-grid-item>
        </up-grid>
        <view
          class="mt-64rpx ml-24rpx mr-24rpx mb-32rpx flex flex-row justify-between items-center"
        >
          <view
            class="w-320rpx h-80rpx bg-hex-e7faf0 text-hex-0AD16D flex justify-center items-center resetBtn"
            @click="filterReset"
            >重置</view
          >
          <view
            class="w-320rpx h-80rpx bg-hex-0AD16D text-hex-ffffff flex justify-center items-center confirmBtn"
            @click="filterConfirm"
            >确认</view
          >
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup>
import pullDown from "@/static/img/account/recharge/pullDown.png";
import pullUp from "@/static/img/account/recharge/pullUp.png";
import close from "@/static/img/account/recharge/close.png";
import { ref, reactive } from "vue";
import { timeFormat } from "@/utils/utils";
import { getAccountDetail, getAccountInfo } from "@/services/account";
import { onShow, onLoad } from "@dcloudio/uni-app";

const dateActive = ref(false);
const filterActive = ref(false);
const rechangeH2o = ref();
const exchangeH2o = ref();

const clickDate = () => {
  dateActive.value = true;
};
const clickFilter = () => {
  filterActive.value = true;
};
const closeFilter = () => {
  filterActive.value = false;
};

const indexList = ref([
  // {
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
  // 	{
  // 		title: "充值",
  // 		value: "+18.00",
  // 		date: "4月30日 15:29:27"
  // 	},
]);

const filterList = reactive([
  {
    value: "ROOM_GIFT",
    text: "房间送礼",
  },
  {
    value: "EXCHANGE_REFUND",
    text: "提现退款",
  },
  {
    value: "POINTS_REVENUE",
    text: "积分收益",
  },
  {
    value: "SYSTEM_DEDUCTION",
    text: "系统扣减",
  },
  {
    value: "PRIVATE_CHAT_GIFT",
    text: "私聊送礼",
  },
  {
    value: "DIAMOND_EXCHANGE",
    text: "钻石兑换",
  },
  {
    value: "POINTS_REFUND",
    text: "积分退款",
  },
  {
    value: "WITHDRAWAL",
    text: "提现",
  },
  {
    value: "WITHDRAWAL_FAILED",
    text: "提现失败",
  },
  {
    value: "ORDER_PROFIT",
    text: "订单收益",
  },
  {
    value: "SYSTEM_ADD",
    text: "系统添加",
  },
]);

const filterDateData = ref("");

const filterItemData = ref({
  value: null,
  text: "全部",
});

const clickIndex = ref(-1);

let pageIndex = 0;

onShow(() => {
  pageIndex = 0;
  let data = getAccountDetail(getFilter(), pageIndex).then((res) => {
    indexList.value = res.list;
  });
  getAccountInfo().then((res) => {
    rechangeH2o.value = res.rechangeH2o;
    exchangeH2o.value = res.exchangeH2o;
  });
});

function getFilter() {
  let result = "";
  if (filterDateData.value) {
    result = timeFormat(filterDateData.value, "yyyy-MM");
  }
  return {
    accountType: "H2O",
    month: result,
    transactionType: filterItemData.value.value,
  };
}

const scrolltolower = () => {
  loadmore();
};

const loadmore = () => {
  pageIndex++;
  getAccountDetail(getFilter(), pageIndex).then((res) => {
    indexList.value = indexList.value.concat(res.list);
  });
};

const filterItemClick = (item, index) => {
  clickIndex.value = index;
};

const dateConfirm = (e) => {
  filterDateData.value = e.value;
  dateActive.value = false;
};

const chooseDateString = () => {
  let result = "";
  if (filterDateData.value) {
    result = timeFormat(filterDateData.value, "yyyy-MM");
  }
  return result;
};

const filterReset = () => {
  clickIndex.value = -1;
};

const filterConfirm = () => {
  if (clickIndex.value < 0) {
    filterItemData.value = {
      value: null,
      text: "全部",
    };
  } else {
    filterItemData.value = filterList[clickIndex.value];
  }
  filterActive.value = false;
  pageIndex = 0;
  let data = getAccountDetail(getFilter(), pageIndex).then((res) => {
    indexList.value = res.list;
  });
};
</script>

<style scoped lang="scss">
.list {
  height: 100% !important;
}

.filter-item {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  width: calc(100% - 32rpx);
  margin-top: 32rpx;
}

.filter-item-active {
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  width: calc(100% - 32rpx);
  margin-top: 32rpx;
  background-color: #e7faf0;
  color: #0ad16d;
  border-radius: 40rpx;
  border-width: 2rpx;
  border-color: #0ad16d;
}

.resetBtn {
  border-radius: 40rpx;
  border-width: 2rpx;
  border-color: #0ad16d;
}

.confirmBtn {
  border-radius: 40rpx;
  border-width: 2rpx;
}
</style>
