<template>
  <div class="ml-24rpx mr-24rpx mt-36rpx">
    <qh-navbar title="编辑签名" center offset background="white" dark />
    <up-input
      placeholder="请输入签名"
      border="bottom"
      v-model="sign"
      @change="change"
      :maxlength="30"
      clearable
    ></up-input>
    <div class="mt-24rpx flex flex-row justify-between">
      <!-- <span class="text-hex-999 text-20rpx">每自然月用户仅可修改一次昵称</span> -->
      <span class="text-hex-999 text-20rpx">{{ limitString }}</span>
    </div>
    <up-button
      class="mt-104rpx"
      color="#0AD16D"
      type="primary"
      text="保存"
      @click="save"
    ></up-button>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { getUserInfo, updateSignature } from "@/services/account";
import { onShow, onLoad } from "@dcloudio/uni-app";
import { showToast } from "@/utils/utils";
import { useUserStore } from "@/store/user";

const sign = ref();
const buttonDisabled = ref(true);
const limitString = ref("0/30");

onMounted(() => {});

onShow(() => {
  getUserInfo().then((res) => {
    sign.value = res.signature;
    limitString.value = res.signature ? res.signature.length + "/30" : "0/30";
    change(res.signature);
  });
});

const change = (e) => {
  let length = e ? e.length : 0;
  limitString.value = length + "/30";
  buttonDisabled.value = length == 0;
};

const save = () => {
  updateSignature(sign.value).then((res) => {
    let user = useUserStore();
    user.setUserInfo({ signature: sign.value });
    showToast("修改成功");
  });
};
</script>

<style lang="scss" scoped></style>
