framework module ZegoExpressEngine {
  umbrella header "ZegoExpressEngine.h"
  exclude header "cpp/ZegoExpressEventHandler.h"
  exclude header "cpp/ZegoExpressDefines.h"
  exclude header "cpp/ZegoExpressInterface.h"
  exclude header "cpp/ZegoExpressSDK.h"
  exclude header "cpp/ZegoExpressErrorCode.h"
  exclude header "cpp/internal/ZegoInternalPrivate.h"
  exclude header "cpp/internal/ZegoInternalCallbackImpl.hpp"
  exclude header "cpp/internal/ZegoInternalExplicit.hpp"
  exclude header "cpp/internal/ZegoInternalAudioEffectPlayer.hpp"
  exclude header "cpp/internal/ZegoInternalScreenCaptureSource.hpp"
  exclude header "cpp/internal/ZegoInternalEngineImpl.hpp"
  exclude header "cpp/internal/ZegoInternalMediaPlayer.hpp"
  exclude header "cpp/internal/ZegoInternalBridge.h"
  exclude header "cpp/internal/ZegoInternalBase.h"
  exclude header "cpp/internal/ZegoInternalDefines.hpp"
  exclude header "cpp/internal/ZegoInternalRealTimeSequentialDataManager.hpp"
  exclude header "cpp/internal/ZegoInternalAIVoiceChanger.hpp"
  exclude header "cpp/internal/ZegoInternalMediaDataPublisher.hpp"
  exclude header "cpp/internal/ZegoInternalRangeScene.hpp"
  exclude header "cpp/internal/ZegoInternalCopyrightedMusic.hpp"
  exclude header "cpp/internal/ZegoInternalRangeAudio.hpp"
  exclude header "cpp/internal/include/zego-express-device.h"
  exclude header "cpp/internal/include/zego-express-publisher.h"
  exclude header "cpp/internal/include/zego-express-im.h"
  exclude header "cpp/internal/include/zego-express-range-audio.h"
  exclude header "cpp/internal/include/zego-express-preprocess.h"
  exclude header "cpp/internal/include/zego-express-range-scene-stream.h"
  exclude header "cpp/internal/include/zego-express-utilities.h"
  exclude header "cpp/internal/include/zego-express-engine.h"
  exclude header "cpp/internal/include/zego-express-player.h"
  exclude header "cpp/internal/include/zego-express-copyrighted-music.h"
  exclude header "cpp/internal/include/zego-express-media-data-publisher.h"
  exclude header "cpp/internal/include/zego-express-range-scene-team.h"
  exclude header "cpp/internal/include/zego-express-mediaplayer.h"
  exclude header "cpp/internal/include/zego-express-record.h"
  exclude header "cpp/internal/include/zego-express-mixer.h"
  exclude header "cpp/internal/include/zego-express-range-scene.h"
  exclude header "cpp/internal/include/zego-express-errcode.h"
  exclude header "cpp/internal/include/zego-express-custom-video-io.h"
  exclude header "cpp/internal/include/zego-express-ai-voice-changer.h"
  exclude header "cpp/internal/include/zego-express-audio-vad-client.h"
  exclude header "cpp/internal/include/zego-express-custom-audio-io.h"
  exclude header "cpp/internal/include/zego-express-room.h"
  exclude header "cpp/internal/include/zego-express-defines.h"
  exclude header "cpp/internal/include/zego-express-screen-capture.h"
  exclude header "cpp/internal/include/zego-express-range-scene-item.h"
  exclude header "cpp/internal/include/zego-express-audio-effect-player.h"

  export *
  module * { export * }
}
