export declare class ZIMLogger {
    warn(tag: string, action: string, msg?: string | Record<string, any>): void;
    /**
     * Format the log to reduce a large number of invalid logs
     *
     * 1. Uint8Array           ->  b=length
     * 2. Array.length > 2     ->  [length, Array[first], Array[last]]
     * 3. String.length > 128  ->  s=length
     *
     */
    private stringify;
}
export declare enum ZIMLogTag {
    Manager = "MGR",
    Connection = "Conn",
    Database = "DB",
    User = "User",
    Conversation = "Conv",
    Strategy = "ST",
    Room = "Room",
    Group = "Group",
    Call = "Call",
    Friend = "Friend"
}
export declare enum ZIMLogAction {
    CreateEngine = "API.createEngine",
    DestroyEngine = "API.destroyEngine",
    SetLogConfig = "API.setLogConfig",
    UploadLog = "API.uploadLog",
    Login = "API.login",
    Logout = "API.logout",
    RenewToken = "API.renewToken",
    QueryUsersInfo = "API.queryUsersInfo",
    UpdateUserName = "API.updateUserName",
    UpdateUserAvatarUrl = "API.updateUserAvatarUrl",
    UpdateUserExtendedData = "API.updateUserExtendedData",
    UpdateUserOfflinePushRule = "API.updateUserOfflinePushRule",
    QuerySelfUserRule = "API.querySelfUserRule",
    QueryConversation = "API.queryConversation",
    QueryConversationList = "API.queryConversationList",
    QueryPinnedList = "API.queryConversationPinnedList",
    DeleteConversation = "API.deleteConversation",
    ClearUnreadMessageCount = "API.clearConversationUnreadMessageCount",
    SetNotificationStatus = "API.setConversationNotificationStatus",
    UpdatePinnedState = "API.updateConversationPinnedState",
    SetConvDraft = "API.setConversationDraft",
    SendMessage = "API.sendMessage",
    SendMediaMessage = "API.sendMediaMessage",
    DeleteMessages = "API.deleteMessages",
    DeleteAllMessage = "API.deleteAllMessage",
    QueryHistoryMessage = "API.queryHistoryMessage",
    InsertMessageToLocalDB = "API.insertMessageToLocalDB",
    UpdateMessageLocalExtendedData = "API.updateMessageLocalExtendedData",
    SendReceiptRead = "API.sendConversationMessageReceiptRead",
    SendMessageReceiptsRead = "API.sendMessageReceiptsRead",
    QueryReceiptsInfo = "API.queryMessageReceiptsInfo",
    QueryReceiptReadMemberList = "API.queryGroupMessageReceiptReadMemberList",
    QueryReceiptUnreadMemberList = "API.queryGroupMessageReceiptUnreadMemberList",
    RevokeMessage = "API.RevokeMessage",
    QueryCombineMessage = "API.queryCombineMessageDetail",
    AddReaction = "JSAPI.addMessageReaction",
    DeleteReaction = "JSAPI.deleteMessageReaction",
    QueryReaction = "JSAPI.queryMessageReactionUserList",
    CreateRoom = "API.createRoom",
    JoinRoom = "API.joinRoom",
    LeaveRoom = "API.leaveRoom",
    QueryRoomMemberList = "API.queryRoomMemberList",
    QueryRoomMembers = "API.queryRoomMembers",
    QueryRoomOnlineMemberCount = "API.queryRoomOnlineMemberCount",
    SetRoomAttributes = "API.setRoomAttributes",
    DeleteRoomAttributes = "API.deleteRoomAttributes",
    QueryRoomAllAttributes = "API.queryRoomAllAttributes",
    BeginRoomAttributesBatchOperation = "API.beginRoomAttributesBatchOperation",
    EndRoomAttributesBatchOperation = "API.endRoomAttributesBatchOperation",
    SetRoomMembersAttributes = "API.setRoomMembersAttributes",
    QueryRoomMembersAttributes = "API.queryRoomMembersAttributes",
    QueryRoomMemberAttributesList = "API.queryRoomMemberAttributesList",
    CreateGroup = "API.createGroup",
    EnterRoom = "API.enterRoom",
    JoinGroup = "API.joinGroup",
    DismissGroup = "API.dismissGroup",
    LeaveGroup = "API.leaveGroup",
    InviteUsersIntoGroup = "API.inviteUsersIntoGroup",
    KickGroupMembers = "API.kickGroupMembers",
    QueryGroupList = "API.queryGroupList",
    QueryGroupMemberList = "API.queryGroupMemberList",
    QueryGroupMemberCount = "API.queryGroupMemberCount",
    TransferGroupOwner = "API.transferGroupOwner",
    QueryGroupInfo = "API.queryGroupInfo",
    UpdateGroupName = "API.updateGroupName",
    UpdateGroupNotice = "API.updateGroupNotice",
    UpdateGroupAvatarUrl = "API.updateGroupAvatarUrl",
    SetGroupAttributes = "API.setGroupAttributes",
    DeleteGroupAttributes = "API.deleteGroupAttributes",
    QueryGroupAttributes = "API.queryGroupAttributes",
    SetGroupMemberNickname = "API.setGroupMemberNickname",
    SetGroupMemberRole = "API.setGroupMemberRole",
    QueryGroupMemberInfo = "API.queryGroupMemberInfo",
    MuteGroup = "API.muteGroup",
    MuteGroupMembers = "API.muteGroupMembers",
    QueryGroupApp = "API.queryGroupApplicationList",
    UpdateGroupVerifyMode = "API.updateGroupVerifyMode",
    CallInvite = "API.callInvite",
    CallCancel = "API.callCancel",
    CallAccept = "API.callAccept",
    CallReject = "API.callReject",
    CallJoin = "API.callJoin",
    CallQuit = "API.callQuit",
    CallEnd = "API.callEnd",
    CallingInvite = "API.callingInvite",
    QueryCallList = "API.queryCallInvitationList",
    AddFriend = "API.addFriend",
    SendFriendApp = "API.sendFriendApplication",
    DeleteFriends = "API.deleteFriends",
    CheckFriends = "API.checkFriendsRelation",
    UpdateFriendAlias = "API.updateFriendAlias",
    UpdateFriendAttr = "API.updateFriendAttributes",
    AcceptFriendApp = "API.acceptFriendApplication",
    RejectFriendApp = "API.rejectFriendApplication",
    QueryFriendsInfo = "API.queryFriendsInfo",
    QueryFriendList = "API.queryFriendList",
    QueryFriendAppList = "API.queryFriendApplicationList",
    AddUserToBlacklist = "API.addUsersToBlacklist",
    RemoveUsersFromBlacklist = "API.removeUsersFromBlacklist",
    CheckUserIsInBlacklist = "API.checkUserIsInBlacklist",
    QueryBlacklist = "API.queryBlacklist",
    SearchConversations = "API.searchLocalConversations",
    SearchGlobalMessages = "API.searchGlobalLocalMessages",
    SearchMessages = "API.searchLocalMessages",
    SearchGroups = "API.searchLocalGroups",
    SearchGroupMembers = "API.searchLocalGroupMembers",
    SearchFriends = "API.searchLocalFriends",
    SendCMD = "SCMD",
    PushCMD = "PCMD",
    Reconnection = "reconn",
    OpenConnection = "openconn",
    CloseConnection = "closeconn",
    CreateDB = "createDB",
    DeleteDB = "deleteDB",
    WriteDB = "writeDB"
}
