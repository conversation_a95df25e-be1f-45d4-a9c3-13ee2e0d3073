import {
	ref
} from 'vue'
import {
	Iap,
	IapTransactionState
} from "./iap.js"
import {
	onLoad,
	onShow
} from "@dcloudio/uni-app";
import { postCreateOrder, applePayNotify} from "@/services/recharge";
const productIdList = ['h2_6', 'h2_48', 'h2_24', 'h2_198', 'h2_128', 'h2_12','vip_card_30', 'vip_card_365', 'vip_card_90', 'vip_card_7']


export function usePay() {
	const iapChannel = ref('')
	const isCanPay = ref(true)
	const iapInstance = ref()
	const productList = ref([])
	const loading = ref(false)
	const productId = ref()
	onLoad(() => {
		// 创建示例
		// iapInstance.value = new Iap({
		// 	products: productIdList // 苹果开发者中心创建
		// })
		// initIapPay()
	})
	// onShow(() => {
	// 	if (iapInstance.value.ready) {
	// 		restore();
	// 	}
	// })
	async function initIapPay(productIds, orderNo) {
		iapInstance.value = new Iap({
			products: productIds // 苹果开发者中心创建
		})
		console.log('iapInstance.value1',iapInstance.value)
		
		
		uni.showLoading({
			title: '检测支付环境...'
		});

		try {
			// 初始化，获取iap支付通道
			const initData = await iapInstance.value.init();
			console.log('iapInstance.value2', iapInstance.value)
			console.log('初始化Chinnel', initData)
			if (iapInstance.value._ready) {
				await restore();
			}

			// 从苹果服务器获取产品列表
			productList.value = await iapInstance.value.getProduct();
			console.log('productList.valu', productList.value)
			productList.value[0].checked = true;
			productId.value = productList.value[0].productid;
            let result = await startApplePayOrder(orderNo);
			console.log('11111111', result)
			return result

		} catch (e) {
			uni.showModal({
				title: "支付",
				content: e.message,
				showCancel: false
			});
			throw new Error('获取商品列表失败', e)
		} finally {
			uni.hideLoading();
		}

		// if (iapInstance.value.ready) {
		// 	restore();
		// }

	}
	async function restore() {
		// 检查上次用户已支付且未关闭的订单，可能出现原因：首次绑卡，网络中断等异常

		// 在此处检查用户是否登陆

		uni.showLoading({
			title: '正在检测订单...'
		});

		try {
			// 从苹果服务器检查未关闭的订单，可选根据 username 过滤，和调用支付时透传的值一致
			const transactions = await iapInstance.value.restoreCompletedTransactions({
				username: ""
			});
			// console.log('transactions.length', transactions)

			if (!transactions.length) {
				return;
			}

			// 开发者业务逻辑，从服务器获取当前用户未完成的订单列表，和本地的比较
			// 此处省略
      const transaction = transactions[0]
			switch (transaction.transactionState) {
				case IapTransactionState.purchased:
					// 用户已付款，在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
					let result = await validatePaymentResult({
						orderId: transaction.username,
						// username: username,
						receipt: transaction.transactionReceipt, // 不可作为订单唯一标识
						transactionId: transaction.transactionIdentifier
					});
                    console.log('是否验证通过', result)
					// 验证通过，交易结束，关闭订单
					if (result) {
					  await iapInstance.value.finishTransaction(transaction);
					}
					break;
				case IapTransactionState.failed:
					// 关闭未支付的订单
					await iapInstance.value.finishTransaction(transaction);
					break;
				default:
					break;
			}
		} catch (e) {
			console.log('检查订单', e)
			// uni.showModal({
			// 	title: "检查订单状态",
			// 	content: e.message,
			// 	showCancel: false
			// });
			throw new Error(e.message);
			// uni.showModal({
			// 	content: e.message,
			// 	showCancel: false
			// });
		} finally {
			uni.hideLoading();
		}
	}



	async function startApplePayOrder(orderNo) {
		if (loading.value == true) {
			return;
		}
		loading.value = true;

		uni.showLoading({
			title: '支付处理中...'
		});

		try {
			// // 从开发者服务器创建订单
			// const orderResult = await createOrder(createOrederParams);
			// console.log('orderResult--------', orderResult)
			// let {orderNo: orderId} = orderResult

			// 请求苹果支付
			console.log('orderNo，orderNo，orderNo', orderNo)
			const transaction = await iapInstance.value.requestPayment({
				productid: productId.value,
				manualFinishTransaction: true,
				username: orderNo //根据业务需求透传参数，关联用户和订单关系
			});
			
			console.log('transaction,transaction', '请求苹果支付')

			// 在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
			const validateData = await validatePaymentResult({
			  orderId: orderNo,
			  // username: username,
			  receipt: transaction.transactionReceipt, // 不可作为订单唯一标识
			  transactionId: transaction.transactionIdentifier
			});
			console.log(validateData)
			if(validateData==='success') {
				// 验证成功后关闭订单
				await iapInstance.value.finishTransaction(transaction);
				return 'success'
				
			} else {
			   throw new Error('服务器端请求苹果服务器验证票据失败')
			  
			}
			

			// 支付成功
		} catch (e) {
			// uni.showModal({
			// 	content: e.message,
			// 	showCancel: false
			// });
			throw new Error('支付失败', e)
		} finally {
			loading.value = false;
			uni.hideLoading();
		}



	}

	async function createOrder(params) {
		let result = await postCreateOrder(params)
		return result
	}

	async function validatePaymentResult(params) {
		try{
			// console.log('----params---', params)
			return await applePayNotify(params)
		}catch(e) {
			console.log('验证订单', e)
			throw new Error(`验证订单失败`, e)
		}
		
	}

	function applePriceChange(e) {
		this.productId = e.detail.value;
	}





	return {
		startApplePayOrder,
		initIapPay
	}

}