<template>
	<ZegoExpress-Local-View :viewMode="viewMode" :channel="channel" :canvasType="canvasType"></ZegoExpress-Local-View>
</template>

<script>
	import {
		ZegoPublishChannel,
		ZegoViewMode
	} from '../lib/ZegoExpressDefines';

	export default {
		name: 'ZegoLocalView',
		props: {
			viewMode: {
				type: Number,
				required: false,
				default: ZegoViewMode.AspectFit
			},
			channel: {
				type: Number,
				required: false,
				default: ZegoPublishChannel.Main
			},
			canvasType: {
				type: Number,
				required: false,
				default: 0
			}
		},
	}
</script>

<style>

</style>
