<template>
  <div class="page bg-hex-f5f5f5">
    <qh-navbar title="通讯录黑名单" center offset background="#f5f5f5" dark />
    <qh-loadmore ref="moreRef" :fn="blackList" v-slot="{ list }">
      <List :list="list" @remove="removeItem" />
    </qh-loadmore>
    <qh-safearea />
    <!-- <view class="mb-0 mt-0 ml-0 mr-0">
      <up-list class="bg-white">
        <up-list-item v-for="(item, index) in list" :key="index">
          <up-cell>
            <template #icon>
              <up-avatar
                shape="square"
                size="35"
                :src="item.blackUserAvatar"
                customStyle="margin: -3px 5px -3px 0"
              ></up-avatar>
            </template>
            <template #title>
              <view>{{ item.blackUserName }}</view>
            </template>
            <template #value>
              <up-button
                text="移除"
                :plain="true"
                shape="circle"
                iconColor="#0AD16D"
                @click="removeItem(item)"
              ></up-button>
            </template>
          </up-cell>
        </up-list-item>
      </up-list>
    </view> -->
  </div>
</template>

<script setup>
import { blackList } from "@/services/account";
import { useUserStore } from "@/store/user";
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import List from "./_components/List";
import { blackRemove } from "@/services/account";

const userStore = useUserStore();

const list = ref([]);

const moreRef = ref();

const removeItem = (item) => {
  blackRemove(item.blackUserId).then((res) => {
    moreRef.value && moreRef.value.refetch();
  });
};
</script>

<style lang="scss" scoped>
.radius-item {
  border-radius: 16rpx;
}
</style>
