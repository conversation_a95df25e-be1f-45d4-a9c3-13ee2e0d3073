<template>
  <div v-show="renderTag && renderTag.text"
    :style="{ backgroundColor: renderTag.backgroundColor, color: renderTag.fontColor }"
    class="rounded-[8rpx] text-24rpx leading-36rpx px-8rpx">
    {{ renderTag.text }}
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import { useAppStore } from '../../store/app';

const props = defineProps({
  tag: {
    type: Number,
    default: 0
  },
  text: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: "#0AD16D"
  }
})


const store = useAppStore();
watch(() => store.tags, console.log)
const renderTag = computed(() => {
  if (!props.tag) {
    return {
      text: props.text,
      backgroundColor: props.color + '26',
      fontColor: props.color,
    }
  } else {
    const item = store.tags.find(item => item.id === props.tag)
    return {
      ...item,
      text: item?.name
    }
  }
})

</script>
