// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export async function useCallModule(log, time) {
    const zim = ZIM.getInstance();

    try {
        const res = await zim.callInvite(['tg002', 'tg003'], { timeout: 10, extendedData: time });
        log('callInvite', res);
        setTimeout(() => {
            zim.callCancel(['tg002'], res.callID, { extendedData: time })
                .then(log.bind(null, 'callCancel'))
                .catch(log.bind(null, 'callCancel'));
        }, 1000);
    } catch (error) {
        log('callInvite', error);
    }
}
