export interface ZIMError {
    code: Z<PERSON><PERSON><PERSON>rCode;
    message: string;
}
export declare enum ZIMErrorCode {
    Success = 0,
    Failed = 1,
    CommonModuleParamInvalid = 6000001,
    CommonModuleNotInit = 6000002,
    CommonModuleInvalidAppID = 6000003,
    CommonModuleTriggerSDKFrequencyLimit = 6000004,
    CommonModuleTriggerServerFrequencyLimit = 6000005,
    CommonModuleSwitchServerError = 6000006,
    CommonModuleIMServerError = 6000007,
    CommonModuleIMDatabaseError = 6000008,
    CommonModuleIMServerDisconnect = 6000009,
    CommonModuleUploadLogError = 6000010,
    CommonModuleUserIsNotExist = 6000011,
    NetworkModuleCommonError = 6000101,
    NetworkModuleServerError = 6000102,
    NetworkModuleTokenInvalid = 6000103,
    NetworkModuleNetworkError = 6000104,
    NetworkModuleTokenExpired = 6000106,
    NetworkModuleTokenVersionError = 6000107,
    NetworkModuleTokenTimeIsTooShort = 6000108,
    NetworkModuleUserHasAlreadyLogged = 6000111,
    NetworkModuleUserIsNotLogged = 6000121,
    MessageModuleCommonError = 6000201,
    MessageModuleServerError = 6000202,
    MessageModuleSendMessageFailed = 6000203,
    MessageModuleTargetDoesNotExist = 6000204,
    MessageModuleCallError = 6000270,
    MessageModuleCancelCallError = 6000271,
    MessageModuleCallServerError = 6000272,
    MessageModuleIsNotInvitor = 6000273,
    MessageModuleIsNotInvitee = 6000274,
    MessageModuleCallAlreadyExists = 6000275,
    MessageModuleCallDoesNotExist = 6000276,
    MessageModuleAuditRejected = 6000221,
    MessageModuleAuditFailed = 6000222,
    ConversationModuleCommonError = 6000601,
    ConversationModuleServerError = 6000602,
    ConversationModuleConversationDoesNotExist = 6000603,
    RoomModuleCommonError = 6000301,
    RoomModuleServerError = 6000302,
    RoomModuleCreateRoomError = 6000303,
    RoomModuleJoinRoomError = 6000304,
    RoomModuleLeaveRoomError = 6000306,
    RoomModuleUserIsNotInTheRoom = 6000321,
    RoomModuleTheRoomDoesNotExist = 6000322,
    RoomModuleTheRoomAlreadyExists = 6000323,
    RoomModuleTheNumberOfExistingRoomsHasReachedLimit = 6000324,
    RoomModuleTheNumberOfJoinedRoomsHasReachedLimit = 6000325,
    RoomModuleRoomAttributesCommonError = 6000330,
    RoomModuleRoomAttributesOperationFailedCompletely = 6000331,
    RoomModuleRoomAttributesQueryFailed = 6000333,
    RoomModuleTheNumberOfRoomAttributesExceedsLimit = 6000334,
    RoomModuleTheLengthOfRoomAttributeKeyExceedsLimit = 6000335,
    RoomModuleTheLengthOfRoomAttributeValueExceedsLimit = 6000336,
    RoomModuleTheTotalLengthOfRoomAttributesValueExceedsLimit = 6000337,
    RoomModuleRoomMemberAttributesCommonError = 6000350,
    RoomModuleTheTotalLengthOfRoomMemberAttributesExceedsLimit = 6000351,
    RoomModuleTheLengthOfRoomMemberAttributesKeyExceedsLimit = 6000352,
    RoomModuleTheLengthOfRoomMemberAttributesValueExceedsLimit = 6000353,
    RoomModuleTheMemberNumberOfRoomMemberAttributesExceedsLimit = 6000357,
    GroupModuleCommonError = 6000501,
    GroupModuleServerError = 6000502,
    GroupModuleCreateGroupError = 6000503,
    GroupModuleDismissGroupError = 6000504,
    GroupModuleJoinGroupError = 6000505,
    GroupModuleLeaveGroupError = 6000506,
    GroupModuleKickoutGroupMemberError = 6000507,
    GroupModuleInviteUserIntoGroupError = 6000508,
    GroupModuleTransferOwnerError = 6000509,
    GroupModuleUpdateGroupInfoError = 6000510,
    GroupModuleQueryGroupInfoError = 6000511,
    GroupModuleGroupAttributesOperationFailed = 6000512,
    GroupModuleGroupAttributesQueryFailed = 6000513,
    GroupModuleUpdateGroupMemberInfoError = 6000514,
    GroupModuleQueryGroupMemberInfoError = 6000515,
    GroupModuleQueryGroupListError = 6000516,
    GroupModuleQueryGroupMemberListError = 6000517,
    GroupModuleUserIsNotInTheGroup = 6000521,
    GroupModuleMemberIsAlreadyInTheGroup = 6000522,
    GroupModuleGroupDoesNotExist = 6000523,
    GroupModuleGroupAlreadyExists = 6000524,
    GroupModuleGroupMemberHasReachedLimit = 6000525,
    GroupModuleGroupAttributeDoesNotExist = 6000526,
    GroupModuleTheNumberOfGroupAttributesExceedsLimit = 6000531,
    GroupModuleTheLengthOfGroupAttributeKeyExceedsLimit = 6000532,
    GroupModuleTheLengthOfGroupAttributeValueExceedsLimit = 6000533,
    GroupModuleTheTotalLengthOfGroupAttributesValueExceedsLimit = 6000534,
    GroupModuleNoCorrespondingOperationAuthority = 6000541,
    GroupModuleForbidJoinGroupError = 6000542,
    GroupModuleNeedApplyJoinGroupError = 6000543,
    GroupModuleNeedApplyInviteGroupError = 6000544
}
