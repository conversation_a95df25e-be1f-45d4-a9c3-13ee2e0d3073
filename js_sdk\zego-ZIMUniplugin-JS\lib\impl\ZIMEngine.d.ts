import { ZIMRoomAllLeftResult, ZIMBlacklistCheckedResult, ZIMBlacklistQueriedResult, ZIMBlacklistQueryConfig, ZIMBlacklistUsersAddedResult, ZIMBlacklistUsersRemovedResult, ZIMCallAcceptanceSentResult, ZIMCallAcceptConfig, ZIMCallCancelConfig, ZIMCallCancelSentResult, ZIMCallEndConfig, ZIMCallEndSentResult, ZIMCallingInvitationSentResult, ZIMCallingInviteConfig, ZIMCallInvitationListQueriedResult, ZIMCallInvitationQueryConfig, ZIMCallInvitationSentResult, ZIMCallInviteConfig, ZIMCallJoinConfig, ZIMCallJoinSentResult, ZIMCallQuitConfig, ZIMCallQuitSentResult, ZIMCallRejectConfig, ZIMCallRejectionSentResult, ZIMCombineMessage, ZIMCombineMessageDetailQueriedResult, ZIMConversationDeleteConfig, ZIMConversationDeletedResult, ZIMConversationDraftSetResult, ZIMConversationListQueriedResult, ZIMConversationMessageReceiptReadSentResult, ZIMConversationNotificationStatus, ZIMConversationNotificationStatusSetResult, ZIMConversationPinnedListQueriedResult, ZIMConversationPinnedStateUpdatedResult, ZIMConversationQueriedResult, ZIMConversationQueryConfig, ZIMConversationSearchConfig, ZIMConversationsSearchedResult, ZIMConversationType, ZIMConversationUnreadMessageCountClearedResult, ZIMFileCacheClearConfig, ZIMFileCacheQueriedResult, ZIMFileCacheQueryConfig, ZIMFriendAddConfig, ZIMFriendAddedResult, ZIMFriendAliasUpdatedResult, ZIMFriendApplicationAcceptConfig, ZIMFriendApplicationAcceptedResult, ZIMFriendApplicationListQueriedResult, ZIMFriendApplicationListQueryConfig, ZIMFriendApplicationRejectConfig, ZIMFriendApplicationRejectedResult, ZIMFriendApplicationSendConfig, ZIMFriendApplicationSentResult, ZIMFriendAttributesUpdatedResult, ZIMFriendDeleteConfig, ZIMFriendListQueriedResult, ZIMFriendListQueryConfig, ZIMFriendRelationCheckConfig, ZIMFriendsDeletedResult, ZIMFriendsInfoQueriedResult, ZIMFriendsRelationCheckedResult, ZIMGroupAdvancedConfig, ZIMGroupApplicationListQueriedResult, ZIMGroupApplicationListQueryConfig, ZIMGroupAttributesOperatedResult, ZIMGroupAttributesQueriedResult, ZIMGroupAvatarUrlUpdatedResult, ZIMGroupCreatedResult, ZIMGroupDismissedResult, ZIMGroupInfo, ZIMGroupInfoQueriedResult, ZIMGroupInviteApplicationAcceptedResult, ZIMGroupInviteApplicationRejectedResult, ZIMGroupInviteApplicationSendConfig, ZIMGroupInviteApplicationsSentResult, ZIMGroupJoinApplicationAcceptConfig, ZIMGroupJoinApplicationAcceptedResult, ZIMGroupJoinApplicationRejectConfig, ZIMGroupJoinApplicationRejectedResult, ZIMGroupJoinApplicationSendConfig, ZIMGroupJoinApplicationSentResult, ZIMGroupJoinedResult, ZIMGroupLeftResult, ZIMGroupListQueriedResult, ZIMGroupMemberCountQueriedResult, ZIMGroupMemberInfoQueriedResult, ZIMGroupMemberKickedResult, ZIMGroupMemberListQueriedResult, ZIMGroupMemberMuteConfig, ZIMGroupMemberNicknameUpdatedResult, ZIMGroupMemberQueryConfig, ZIMGroupMemberRoleUpdatedResult, ZIMGroupMemberSearchConfig, ZIMGroupMembersMutedResult, ZIMGroupMembersSearchedResult, ZIMGroupMessageReceiptMemberListQueriedResult, ZIMGroupMessageReceiptMemberQueryConfig, ZIMGroupMuteConfig, ZIMGroupMutedResult, ZIMGroupNameUpdatedResult, ZIMGroupNoticeUpdatedResult, ZIMGroupOwnerTransferredResult, ZIMGroupSearchConfig, ZIMGroupsSearchedResult, ZIMGroupUsersInvitedResult, ZIMGroupVerifyType, ZIMLoginConfig, ZIMMediaDownloadedResult, ZIMMediaDownloadingProgress, ZIMMediaFileType, ZIMMediaMessage, ZIMMediaMessageBase, ZIMMediaMessageSendNotification, ZIMMediaMessageSentResult, ZIMMessage, ZIMMessageBase, ZIMMessageDeleteConfig, ZIMMessageDeletedResult, ZIMMessageExportConfig, ZIMMessageExportingProgress, ZIMMessageImportConfig, ZIMMessageImportingProgress, ZIMMessageInsertedResult, ZIMMessageLocalExtendedDataUpdatedResult, ZIMMessageQueriedResult, ZIMMessageQueryConfig, ZIMMessageReactionAddedResult, ZIMMessageReactionDeletedResult, ZIMMessageReactionUserListQueriedResult, ZIMMessageReactionUserQueryConfig, ZIMMessageReceiptsInfoQueriedResult, ZIMMessageReceiptsReadSentResult, ZIMMessageRevokeConfig, ZIMMessageRevokedResult, ZIMMessageSearchConfig, ZIMMessageSendConfig, ZIMMessageSendNotification, ZIMMessageSentResult, ZIMMessagesGlobalSearchedResult, ZIMMessagesSearchedResult, ZIMRoomAdvancedConfig, ZIMRoomAttributesBatchOperatedResult, ZIMRoomAttributesBatchOperationConfig, ZIMRoomAttributesDeleteConfig, ZIMRoomAttributesOperatedResult, ZIMRoomAttributesQueriedResult, ZIMRoomAttributesSetConfig, ZIMRoomCreatedResult, ZIMRoomEnteredResult, ZIMRoomInfo, ZIMRoomJoinedResult, ZIMRoomLeftResult, ZIMRoomMemberAttributesListQueriedResult, ZIMRoomMemberAttributesQueryConfig, ZIMRoomMemberAttributesSetConfig, ZIMRoomMemberQueriedResult, ZIMRoomMemberQueryConfig, ZIMRoomMembersAttributesOperatedResult, ZIMRoomMembersAttributesQueriedResult, ZIMRoomMembersQueriedResult, ZIMRoomOnlineMemberCountQueriedResult, ZIMSelfUserInfoQueriedResult, ZIMTokenRenewedResult, ZIMUserAvatarUrlUpdatedResult, ZIMUserExtendedDataUpdatedResult, ZIMUserNameUpdatedResult, ZIMUserOfflinePushRule, ZIMUserOfflinePushRuleUpdatedResult, ZIMUsersInfoQueriedResult, ZIMUsersInfoQueryConfig } from '../ZIMDefines';
import { ZIMLogger } from './ZIMLogger';
import { ZIMParamValid } from './ZIMParamValid';
import { ZIMEventHandler } from '../ZIMEventHandler';
declare type MessageAttachedCallback = ZIMMessageSendNotification['onMessageAttached'];
declare type MediaMessageAttachedCallback = ZIMMediaMessageSendNotification['onMessageAttached'];
declare type MediaUploadingProgressCallback = ZIMMediaMessageSendNotification['onMediaUploadingProgress'];
declare type MessageExportingProgressCallback = ZIMMessageExportingProgress['onMessageExportingProgress'];
export declare class ZIMEngine {
    handle: symbol;
    appID: number;
    appSign: string;
    logger: ZIMLogger;
    paramValid: ZIMParamValid;
    loginUserID: string;
    uploadingMap: Map<string, MediaUploadingProgressCallback>;
    downloadingMap: Map<string, ZIMMediaDownloadingProgress>;
    messageAttachedMap: Map<string, MessageAttachedCallback | MediaMessageAttachedCallback>;
    messageExportingMap: Map<string, MessageExportingProgressCallback>;
    eventNameList: string[];
    static _callMethod<T>(method: string, args?: {}): Promise<T>;
    constructor(handle: symbol, appID: number, appSign: string);
    create(): Promise<void>;
    destroy(): Promise<void>;
    uploadLog(): Promise<void>;
    on<K extends keyof ZIMEventHandler>(type: K, listener: ZIMEventHandler[K]): void;
    off<K extends keyof ZIMEventHandler>(type: K): void;
    login(userID: string, config: ZIMLoginConfig): Promise<void>;
    logout(): Promise<void>;
    renewToken(token: string): Promise<ZIMTokenRenewedResult>;
    updateUserName(userName: string): Promise<ZIMUserNameUpdatedResult>;
    updateUserAvatarUrl(userAvatarUrl: string): Promise<ZIMUserAvatarUrlUpdatedResult>;
    updateUserExtendedData(extendedData: string): Promise<ZIMUserExtendedDataUpdatedResult>;
    updateUserOfflinePushRule(offlinePushRule: ZIMUserOfflinePushRule): Promise<ZIMUserOfflinePushRuleUpdatedResult>;
    querySelfUserInfo(): Promise<ZIMSelfUserInfoQueriedResult>;
    queryUsersInfo(userIDs: string[], config: ZIMUsersInfoQueryConfig): Promise<ZIMUsersInfoQueriedResult>;
    queryConversation(conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationQueriedResult>;
    queryConversationList(config: ZIMConversationQueryConfig): Promise<ZIMConversationListQueriedResult>;
    queryConversationPinnedList(config: ZIMConversationQueryConfig): Promise<ZIMConversationPinnedListQueriedResult>;
    deleteConversation(conversationID: string, conversationType: ZIMConversationType, config: ZIMConversationDeleteConfig): Promise<ZIMConversationDeletedResult>;
    deleteAllConversations(config: ZIMConversationDeleteConfig): Promise<void>;
    deleteAllConversationMessages(config: ZIMMessageDeleteConfig): Promise<void>;
    setConversationNotificationStatus(status: ZIMConversationNotificationStatus, conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationNotificationStatusSetResult>;
    updateConversationPinnedState(isPinned: boolean, conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationPinnedStateUpdatedResult>;
    clearConversationUnreadMessageCount(conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationUnreadMessageCountClearedResult>;
    clearConversationTotalUnreadMessageCount(): Promise<void>;
    setConversationDraft(draft: string, conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationDraftSetResult>;
    sendMessage(message: ZIMMessageBase, toConversationID: string, conversationType: ZIMConversationType, config: ZIMMessageSendConfig, notification?: ZIMMessageSendNotification): Promise<ZIMMessageSentResult>;
    sendMediaMessage(message: ZIMMediaMessageBase, toConversationID: string, conversationType: ZIMConversationType, config: ZIMMessageSendConfig, notification?: ZIMMediaMessageSendNotification): Promise<ZIMMediaMessageSentResult>;
    deleteMessages(messageList: ZIMMessage[], conversationID: string, conversationType: ZIMConversationType, config: ZIMMessageDeleteConfig): Promise<ZIMMessageDeletedResult>;
    deleteAllMessage(conversationID: string, conversationType: ZIMConversationType, config: ZIMMessageDeleteConfig): Promise<ZIMMessageDeletedResult>;
    insertMessageToLocalDB(message: ZIMMessageBase | ZIMMediaMessageBase, conversationID: string, conversationType: ZIMConversationType, senderUserID: string): Promise<ZIMMessageInsertedResult>;
    updateMessageLocalExtendedData(localExtendedData: string, message: ZIMMessage): Promise<ZIMMessageLocalExtendedDataUpdatedResult>;
    sendConversationMessageReceiptRead(conversationID: string, conversationType: ZIMConversationType): Promise<ZIMConversationMessageReceiptReadSentResult>;
    sendMessageReceiptsRead(messageList: ZIMMessage[], conversationID: string, conversationType: ZIMConversationType): Promise<ZIMMessageReceiptsReadSentResult>;
    queryMessageReceiptsInfo(messageList: ZIMMessage[], conversationID: string, conversationType: ZIMConversationType): Promise<ZIMMessageReceiptsInfoQueriedResult>;
    queryGroupMessageReceiptMemberList(message: ZIMMessage, groupID: string, config: ZIMGroupMessageReceiptMemberQueryConfig, read: boolean): Promise<ZIMGroupMessageReceiptMemberListQueriedResult>;
    revokeMessage(message: ZIMMessage, config: ZIMMessageRevokeConfig): Promise<ZIMMessageRevokedResult>;
    queryCombineMessageDetail(message: ZIMCombineMessage): Promise<ZIMCombineMessageDetailQueriedResult>;
    queryHistoryMessage(conversationID: string, conversationType: ZIMConversationType, config: ZIMMessageQueryConfig): Promise<ZIMMessageQueriedResult>;
    downloadMediaFile(message: ZIMMediaMessage, fileType: ZIMMediaFileType, progress: ZIMMediaDownloadingProgress): Promise<ZIMMediaDownloadedResult>;
    addMessageReaction(reactionType: string, message: ZIMMessage): Promise<ZIMMessageReactionAddedResult>;
    deleteMessageReaction(reactionType: string, message: ZIMMessage): Promise<ZIMMessageReactionDeletedResult>;
    queryMessageReactionUserList(message: ZIMMessage, config: ZIMMessageReactionUserQueryConfig): Promise<ZIMMessageReactionUserListQueriedResult>;
    createRoom(roomInfo: ZIMRoomInfo, config?: ZIMRoomAdvancedConfig): Promise<ZIMRoomCreatedResult>;
    enterRoom(roomInfo: ZIMRoomInfo, config?: ZIMRoomAdvancedConfig): Promise<ZIMRoomEnteredResult>;
    joinRoom(roomID: string): Promise<ZIMRoomJoinedResult>;
    leaveRoom(roomID: string): Promise<ZIMRoomLeftResult>;
    queryRoomMemberList(roomID: string, config: ZIMRoomMemberQueryConfig): Promise<ZIMRoomMemberQueriedResult>;
    queryRoomMembers(userIDs: string[], roomID: string): Promise<ZIMRoomMembersQueriedResult>;
    queryRoomOnlineMemberCount(roomID: string): Promise<ZIMRoomOnlineMemberCountQueriedResult>;
    queryRoomAllAttributes(roomID: string): Promise<ZIMRoomAttributesQueriedResult>;
    setRoomAttributes(roomAttributes: Record<string, string>, roomID: string, config: ZIMRoomAttributesSetConfig): Promise<ZIMRoomAttributesOperatedResult>;
    deleteRoomAttributes(keys: string[], roomID: string, config: ZIMRoomAttributesDeleteConfig): Promise<ZIMRoomAttributesOperatedResult>;
    beginRoomAttributesBatchOperation(roomID: string, config: ZIMRoomAttributesBatchOperationConfig): Promise<void>;
    endRoomAttributesBatchOperation(roomID: string): Promise<ZIMRoomAttributesBatchOperatedResult>;
    setRoomMembersAttributes(attributes: Record<string, string>, userIDs: string[], roomID: string, config: ZIMRoomMemberAttributesSetConfig): Promise<ZIMRoomMembersAttributesOperatedResult>;
    queryRoomMembersAttributes(userIDs: string[], roomID: string): Promise<ZIMRoomMembersAttributesQueriedResult>;
    queryRoomMemberAttributesList(roomID: string, config: ZIMRoomMemberAttributesQueryConfig): Promise<ZIMRoomMemberAttributesListQueriedResult>;
    leaveAllRoom(): Promise<ZIMRoomAllLeftResult>;
    createGroup(groupInfo: ZIMGroupInfo, userIDs: string[], config?: ZIMGroupAdvancedConfig): Promise<ZIMGroupCreatedResult>;
    joinGroup(isJoin: boolean, groupID: string, config?: ZIMGroupJoinApplicationSendConfig): Promise<ZIMGroupJoinedResult | ZIMGroupJoinApplicationSentResult>;
    leaveGroup(groupID: string): Promise<ZIMGroupLeftResult>;
    dismissGroup(groupID: string): Promise<ZIMGroupDismissedResult>;
    queryGroupList(): Promise<ZIMGroupListQueriedResult>;
    updateGroupNotice(groupNotice: string, groupID: string): Promise<ZIMGroupNoticeUpdatedResult>;
    updateGroupName(groupName: string, groupID: string): Promise<ZIMGroupNameUpdatedResult>;
    updateGroupAvatarUrl(groupAvatarUrl: string, groupID: string): Promise<ZIMGroupAvatarUrlUpdatedResult>;
    muteGroup(isMute: boolean, groupID: string, config: ZIMGroupMuteConfig): Promise<ZIMGroupMutedResult>;
    muteGroupMembers(isMute: boolean, userIDs: string[], groupID: string, config: ZIMGroupMemberMuteConfig): Promise<ZIMGroupMembersMutedResult>;
    queryGroupInfo(groupID: string): Promise<ZIMGroupInfoQueriedResult>;
    setGroupAttributes(groupAttributes: Record<string, string>, groupID: string): Promise<ZIMGroupAttributesOperatedResult>;
    deleteGroupAttributes(keys: string[], groupID: string): Promise<ZIMGroupAttributesOperatedResult>;
    queryGroupAttributes(keys: string[], groupID: string): Promise<ZIMGroupAttributesQueriedResult>;
    queryGroupAllAttributes(groupID: string): Promise<ZIMGroupAttributesQueriedResult>;
    setGroupMemberNickname(nickname: string, forUserID: string, groupID: string): Promise<ZIMGroupMemberNicknameUpdatedResult>;
    setGroupMemberRole(role: number, forUserID: string, groupID: string): Promise<ZIMGroupMemberRoleUpdatedResult>;
    transferGroupOwner(toUserID: string, groupID: string): Promise<ZIMGroupOwnerTransferredResult>;
    queryGroupMemberInfo(userID: string, groupID: string): Promise<ZIMGroupMemberInfoQueriedResult>;
    inviteUsersIntoGroup(isApply: boolean, userIDs: string[], groupID: string, config?: ZIMGroupInviteApplicationSendConfig): Promise<ZIMGroupUsersInvitedResult | ZIMGroupInviteApplicationsSentResult>;
    kickGroupMembers(userIDs: string[], groupID: string): Promise<ZIMGroupMemberKickedResult>;
    queryGroupMemberList(groupID: string, config: ZIMGroupMemberQueryConfig): Promise<ZIMGroupMemberListQueriedResult>;
    queryGroupMemberCount(groupID: string): Promise<ZIMGroupMemberCountQueriedResult>;
    acceptGroupApply(isJoin: boolean, userID: string, groupID: string, config: ZIMGroupJoinApplicationAcceptConfig): Promise<ZIMGroupJoinApplicationAcceptedResult | ZIMGroupInviteApplicationAcceptedResult>;
    rejectGroupApply(isJoin: boolean, userID: string, groupID: string, config: ZIMGroupJoinApplicationRejectConfig): Promise<ZIMGroupJoinApplicationRejectedResult | ZIMGroupInviteApplicationRejectedResult>;
    queryGroupApplyList(config: ZIMGroupApplicationListQueryConfig): Promise<ZIMGroupApplicationListQueriedResult>;
    updateGroupVerifyMode(mode: number, groupID: string, type: ZIMGroupVerifyType): Promise<any>;
    callInvite(invitees: string[], config: ZIMCallInviteConfig): Promise<ZIMCallInvitationSentResult>;
    callCancel(invitees: string[], callID: string, config: ZIMCallCancelConfig): Promise<ZIMCallCancelSentResult>;
    callAccept(callID: string, config: ZIMCallAcceptConfig): Promise<ZIMCallAcceptanceSentResult>;
    callReject(callID: string, config: ZIMCallRejectConfig): Promise<ZIMCallRejectionSentResult>;
    callQuit(callID: string, config: ZIMCallQuitConfig): Promise<ZIMCallQuitSentResult>;
    callEnd(callID: string, config: ZIMCallEndConfig): Promise<ZIMCallEndSentResult>;
    callJoin(callID: string, config: ZIMCallJoinConfig): Promise<ZIMCallJoinSentResult>;
    callingInvite(invitees: string[], callID: string, config: ZIMCallingInviteConfig): Promise<ZIMCallingInvitationSentResult>;
    queryCallInvitationList(config: ZIMCallInvitationQueryConfig): Promise<ZIMCallInvitationListQueriedResult>;
    searchConversations(config: ZIMConversationSearchConfig): Promise<ZIMConversationsSearchedResult>;
    searchGlobalMessages(config: ZIMMessageSearchConfig): Promise<ZIMMessagesGlobalSearchedResult>;
    searchMessages(conversationID: string, conversationType: ZIMConversationType, config: ZIMMessageSearchConfig): Promise<ZIMMessagesSearchedResult>;
    searchGroups(config: ZIMGroupSearchConfig): Promise<ZIMGroupsSearchedResult>;
    searchGroupMembers(groupID: string, config: ZIMGroupMemberSearchConfig): Promise<ZIMGroupMembersSearchedResult>;
    addFriend(userID: string, config: ZIMFriendAddConfig): Promise<ZIMFriendAddedResult>;
    sendFriendApplication(userID: string, config: ZIMFriendApplicationSendConfig): Promise<ZIMFriendApplicationSentResult>;
    deleteFriends(userIDs: string[], config: ZIMFriendDeleteConfig): Promise<ZIMFriendsDeletedResult>;
    checkFriendsRelation(userIDs: string[], config: ZIMFriendRelationCheckConfig): Promise<ZIMFriendsRelationCheckedResult>;
    updateFriendAlias(friendAlias: string, userID: string): Promise<ZIMFriendAliasUpdatedResult>;
    updateFriendAttributes(friendAttributes: Record<string, string>, userID: string): Promise<ZIMFriendAttributesUpdatedResult>;
    acceptFriendApplication(userID: string, config: ZIMFriendApplicationAcceptConfig): Promise<ZIMFriendApplicationAcceptedResult>;
    rejectFriendApplication(userID: string, config: ZIMFriendApplicationRejectConfig): Promise<ZIMFriendApplicationRejectedResult>;
    queryFriendsInfo(userIDs: string[]): Promise<ZIMFriendsInfoQueriedResult>;
    queryFriendList(config: ZIMFriendListQueryConfig): Promise<ZIMFriendListQueriedResult>;
    queryFriendApplicationList(config: ZIMFriendApplicationListQueryConfig): Promise<ZIMFriendApplicationListQueriedResult>;
    addUsersToBlacklist(userIDs: string[]): Promise<ZIMBlacklistUsersAddedResult>;
    removeUsersFromBlacklist(userIDs: string[]): Promise<ZIMBlacklistUsersRemovedResult>;
    checkUserIsInBlacklist(userID: string): Promise<ZIMBlacklistCheckedResult>;
    queryBlacklist(config: ZIMBlacklistQueryConfig): Promise<ZIMBlacklistQueriedResult>;
    exportLocalMessages(folderPath: string, config: ZIMMessageExportConfig, progress?: ZIMMessageExportingProgress): Promise<void>;
    importLocalMessages(folderPath: string, config: ZIMMessageImportConfig, progress?: ZIMMessageImportingProgress): Promise<void>;
    queryLocalFileCache(config: ZIMFileCacheQueryConfig): Promise<ZIMFileCacheQueriedResult>;
    clearLocalFileCache(config: ZIMFileCacheClearConfig): Promise<void>;
}
export {};
