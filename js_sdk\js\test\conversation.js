// #ifdef APP-PLUS
import ZIM from '../js_sdk/zego-ZIMUniplugin-JS/lib';
// #endif

// #ifdef H5
import { ZIM } from '../assets/js/zego-zim-web';
// #endif

// #ifdef MP
import { ZIM } from '../assets/js/zego-zim-miniprogram';
// #endif

export async function useConversationModule(log, time) {
    const zim = ZIM.getInstance();

    const notification = {
        onMessageAttached: log.bind(null, 'onMessageAttached'),
        onMediaUploadingProgress: log.bind(null, 'onMediaUploadingProgress'),
    };

    const config = { priority: 1 };

    const strAarr = Array.from(unescape(encodeURIComponent('==bytemsg会话第三方==' + time)));
    const u8 = new Uint8Array(strAarr.map((c) => c.charCodeAt(0)));
    zim.sendMessage({ type: 2, message: u8 }, 'tg002', 0, config, notification)
        .then(log.bind(null, 'sendCmdMessage'))
        .catch(log.bind(null, 'sendCmdMessage'));
    zim.sendMessage({ type: 1, message: '==msg会话第三方==' + time }, 'tg002', 0, config, notification)
        .then(log.bind(null, 'sendMessage'))
        .catch(log.bind(null, 'sendMessage'));
    zim.sendMediaMessage(
        { type: 11, fileDownloadUrl: 'https://==testfileDownloadUrl==.jpeg' },
        'tg002',
        0,
        config,
        notification,
    )
        .then(log.bind(null, 'sendMediaMessage'))
        .catch(log.bind(null, 'sendMediaMessage'));
    zim.insertMessageToLocalDB({ type: 1, message: '==insert==' + time }, 'tg002', 0, 'tg001')
        .then(log.bind(null, 'insertMessageToLocalDB'))
        .catch(log.bind(null, 'insertMessageToLocalDB'));

    setTimeout(async () => {
        try {
            let res = await zim.queryHistoryMessage('tg002', 0, { count: 10, reverse: true, nextMessage: null });
            log('queryHistoryMessage', res);

            res = await zim.deleteMessages(res.messageList.slice(0, 2), 'tg002', 0, {
                isAlsoDeleteServerMessage: true,
            });
            log('deleteMessages', res);

            res = await zim.queryHistoryMessage('tg002', 0, { count: 10, reverse: true, nextMessage: null });
            log('queryHistoryMessage2', res);

            res = await zim.deleteAllMessage('tg002', 0, { isAlsoDeleteServerMessage: false });
            log('deleteAllMessage', res);

            res = await zim.queryHistoryMessage('tg002', 0, { count: 10, reverse: true, nextMessage: null });
            log('queryHistoryMessage3', res);
        } catch (error) {
            log('queryHistoryMessage', error);
        }
    }, 2000);

    // 会话
    zim.queryConversationList({ count: 10, nextConversation: null })
        .then((res) => {
            log('queryConversationList', res);
            res.conversationList.some((conv) => {
                if (conv.type == 2) {
                    zim.setConversationNotificationStatus(2, conv.conversationID, 2)
                        .then((res) => {
                            log('setConversationNotificationStatus', res);
                            zim.deleteConversation(conv.conversationID, 2, { isAlsoDeleteServerConversation: false })
                                .then(log.bind(null, 'deleteConversation'))
                                .catch(log.bind(null, 'deleteConversation'));
                        })
                        .catch(log.bind(null, 'setConversationNotificationStatus'));
                    return true;
                }
            });
        })
        .catch(log.bind(null, 'queryConversationList'));
    zim.clearConversationUnreadMessageCount('tg002', 0)
        .then(log.bind(null, 'clearConversationUnreadMessageCount'))
        .catch(log.bind(null, 'clearConversationUnreadMessageCount'));
}
