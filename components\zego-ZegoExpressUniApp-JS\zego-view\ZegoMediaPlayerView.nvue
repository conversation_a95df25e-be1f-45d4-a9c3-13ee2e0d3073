<template>
	<ZegoExpress-Mediaplayer-View :playerID="playerID" :canvasType="canvasType" :alphaBlend="alphaBlend"></ZegoExpress-Mediaplayer-View>
</template>

<script>
	export default {
		name: 'ZegoMediaplayerView',
		props: {
			playerID: {
				type: Number,
				required: false,
				default: undefined
			},
			canvasType: {
				type: Number,
				required: false,
				default: 0
			},
			alphaBlend: {
				type: Boolean,
				required: false,
				default: false
			}
		},
	}
</script>

<style>

</style>
