<template>
  <div class="page">
    <qh-navbar title="绑定手机号" center offset border background="white" dark />
    <div class="flex flex-col items-center pt-80rpx">
      <div class="button">
        <span class="text-hex-333 ml-36rpx">+86</span>
        <div class="w-2rpx h-32rpx bg-hex-e5e5e5 ml-18rpx mr-36rpx"></div>
        <input class="text-left text-hex-333 flex-1" placeholder-style="color:#ccc" type="number" maxlength="11"
          placeholder="请输入手机号码" focus v-model="phone" />
      </div>
      <div class="button mt-56rpx">
        <input class="text-left text-hex-333 flex-1 pl-32rpx" placeholder-style="color:#ccc" type="number" maxlength="6"
          placeholder="请输入验证码" v-model="code" :focus="codeFocus" @blur="codeFocus = false" />
        <span class="text-primary text-28rpx px-32rpx" @click="onVerify">{{ countdown ? `${countdown}s后重新获取` : '获取验证码'
        }}</span>
      </div>
      <div class="button mt-80rpx active"
        :class="phone.length === 11 && code.length === 6 ? 'opacity-100' : 'opacity-50'" @click="onBind">
        绑定手机号</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { postVerifyCode, postBindPhone } from '../../../services/account';
import { showToast } from '../../../utils/utils';

const phone = ref('')
const code = ref('')

let timer = 0
const codeFocus = ref(false)
const countdown = ref(0);
const resetCodeInput = () => {
  code.value = ''
  clearInterval(timer)
  countdown.value = 60
  timer = setInterval(() => {
    countdown.value--;
    if (countdown.value === 0) {
      clearInterval(timer)
    }
  }, 1000)
  codeFocus.value = true
}

const onVerify = async () => {
  if (countdown.value) return;
  if (!/^1[3456789]\d{9}$/.test(phone.value)) {
    return showToast('请输入正确的手机号码')
  }
  // 请求验证码
  await postVerifyCode(phone.value);
  resetCodeInput();
}

const onBind = async () => {
  if (phone.value.length !== 11 || code.value.length !== 6) {
    return
  }
  await postBindPhone(phone.value, code.value);
  setTimeout(() => {
    uni.navigateBack();
  }, 1000)
}

</script>

<style lang="scss" scoped>
.button {
  width: 590rpx;
  height: 96rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 48rpx;
  font-size: 32rpx;
  color: #ccc;
  background-color: #F7F7F7;

  &.active {
    background-color: #0AD16D;
    color: white;
  }
}
</style>