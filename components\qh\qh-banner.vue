<template>
  <swiper circular autoplay :interval="3000" :duration="500" v-bind="$attrs">
    <swiper-item v-for="item in list" :key="item.id">
      <img
        :src="item.cover"
        @click="onLinkFn(item)"
        class="object-cover w-full h-full"
        mode="aspectFill"
      />
    </swiper-item>
  </swiper>
</template>

<script setup>
import { getBanners } from "../../services/banner";
import { useRequestOnMounted } from "../../utils/use";
const props = defineProps({
  mark: String,
});
const list = useRequestOnMounted(() => getBanners(props.mark), []);
const onLinkFn = (val) => {
  // 1APP内跳转,2小程序跳转,3H5跳转
  if (val.adType === 1) {
    uni.navigateTo({ url: val.link });
  } else {
    // #ifdef APP-PLUS
    plus.runtime.openWeb(val.link);
    // #endif
  }
};
</script>

<style lang="scss" scoped></style>
