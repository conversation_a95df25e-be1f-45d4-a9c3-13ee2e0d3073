<template>
	<ZegoExpress-Remote-View :viewMode="viewMode" :streamID="streamID" :canvasType="canvasType"></ZegoExpress-Remote-View>
</template>

<script>
	import {
		ZegoViewMode
	} from '../lib/ZegoExpressDefines';

	export default {
		name: 'ZegoRemoteView',
		props: {
			viewMode: {
				type: Number,
				required: false,
				default: ZegoViewMode.AspectFit
			},
			canvasType: {
				type: Number,
				required: false,
				default: 0
			},
			streamID: {
				type: String,
				required: false
			}
		},
	}
	
</script>

<style>

</style>
